FROM php:8.2-apache-bullseye

RUN apt-get update && apt-get install -y \
    ssl-cert \
    libzip-dev \
    libonig-dev \
    libxml2-dev \
    libpng-dev \
    zlib1g-dev \
    wget \
    git \
    libmemcached-dev \
    librabbitmq-dev

RUN docker-php-ext-install \
    pdo_mysql \
    soap \
    xml \
    mbstring \
    gd \
    zip \
    mysqli \
    opcache

RUN docker-php-ext-enable opcache

RUN pecl install apcu && docker-php-ext-enable apcu
RUN pecl install mongodb-1.21.0 && docker-php-ext-enable mongodb

RUN pecl install memcached && docker-php-ext-enable memcached

# Install and enable AMQP extension for RabbitMQ
RUN pecl install amqp && docker-php-ext-enable amqp

RUN a2enmod rewrite
RUN a2enmod ssl

RUN service apache2 restart
RUN a2ensite default-ssl
RUN service apache2 restart

RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

EXPOSE 80
EXPOSE 443