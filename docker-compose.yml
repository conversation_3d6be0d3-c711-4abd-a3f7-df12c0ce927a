version: '2'
services:
  app:
    build:
      context: ./
      dockerfile: ./docker/app/Dockerfile
    links:
      - db
    networks:
      viengine_vpcbr:
        ipv4_address: *********
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./website:/var/www/html
    depends_on:
      - memcached
  db:
    hostname: viengine-db-server
    build:
      context: ./
      dockerfile: ./docker/db/Dockerfile
    networks:
      viengine_vpcbr:
        ipv4_address: *********0
    restart: "no"
    ports:
      - "33061:3306"
    volumes:
      - ./docker/db/mysql-data:/var/lib/mysql
      - ./docker/db/mysql-conf:/etc/mysql
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_USER: docker
      MYSQL_PASSWORD: docker
      MYSQL_DATABASE: vidb_v2
  mongodb:
    image: mongo
    container_name: mongodb
    restart: "no"
    ports:
      - "27017:27017"
    volumes:
      - ./docker/db/mongodb-data:/data/db
    networks:
      viengine_vpcbr:
        ipv4_address: *********1
  memcached:
    image: memcached:latest
    container_name: memcached
    ports:
      - "11211:11211"
    command: memcached -m 64 -p 11211 -u memcache
    networks:
      viengine_vpcbr:
        ipv4_address: *********2
networks:
  viengine_vpcbr:
    driver: bridge
    ipam:
      config:
        - subnet: *********/16
          gateway: *********
volumes:
  mongodb_data:
    driver: local