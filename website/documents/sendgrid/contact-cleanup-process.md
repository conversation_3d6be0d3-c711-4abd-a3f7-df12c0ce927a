## 🧭 Roadmap: SendGrid Contact Cleanup Process

### 1. Collect List IDs

Gather the `list_id`s for each customer site you've created in SendGrid. These IDs are essential for identifying and managing contacts associated with each site.

### 2. Initiate CSV Export Requests

Use the collected `list_id`s to initiate CSV export requests via SendGrid's [Export Contacts API](https://www.twilio.com/docs/sendgrid/api-reference/contacts/export-contacts). This will generate CSV files containing the contacts from the specified lists.

### 3. Queue Requests in RabbitMQ

Add the export requests to a message queue system like RabbitMQ. This allows for asynchronous and scalable processing of the export tasks.

### 4. Monitor CSV Export Status

The processes in RabbitMQ should monitor the status of each export request. Utilize SendGrid's [Export Contacts Status API](https://www.twilio.com/docs/sendgrid/api-reference/contacts/export-contacts-status) to check when the CSV files are ready for download.

### 5. Save Records to Database

Once the CSV files are ready, download them and store the contact information in your database. This step is crucial for retrieving necessary identifiers like `contact_id` for subsequent deletion.

### 6. Delete Contacts from SendGrid

Using the `contact_id`s stored in your database, delete the corresponding contacts from SendGrid. Employ SendGrid's [Bulk Delete Contacts API](https://www.twilio.com/docs/sendgrid/api-reference/contacts/delete-contacts) to perform this operation efficiently.