<?php

const ENV = "development";
const ENV_DB_HOST = "**********";
const ENV_DB_DATABASE = "vidb_v2";
const ENV_DB_USER = "root";
const ENV_DB_PASS = "root";
const ENV_DB_PORT = 3306;
const ENV_MONGO_HOST = '**********';
const ENV_MONGO_USER = '';
const ENV_MONGO_PASS = '';
const ENV_MONGO_DATABASE = 'vistream';
const ENV_MEMCACHED_HOST = "**********";
const ENV_REDIS_HOST = "127.0.0.1";
const ENV_CHAT_SERVER = "https://vistreamchat-test.vistream.tv/"; // URL'nin sonu "/" ile bitmeli
const ENV_POLLING_SERVER = "https://vistreamsocket-test.vistream.tv/"; // URL'nin sonu "/" ile bitmeli
const ENV_SESSION_DRIVER = "files";
const ENV_SESSION_SAVE_PATH = "/tmp";

// RabbitMQ Configuration
const ENV_RABBITMQ_HOST = "**********";
const ENV_RABBITMQ_PORT = 5672;
const ENV_RABBITMQ_USER = "vistream";
const ENV_RABBITMQ_PASS = "vistream";
const ENV_RABBITMQ_VHOST = "/";

const ENV_REACTION_SERVER = "";
const ENV_SENDGRID_APIKEY = "";

const ENV_VIPORTAL_URL = "https://viportal-test.vidizayn.com/";
const ENV_DATALAKE_URL = "http://host.docker.internal:8000/";
const ENV_DATALAKE_AUTH_USER = "admin";
const ENV_DATALAKE_AUTH_PASS = "secret";

