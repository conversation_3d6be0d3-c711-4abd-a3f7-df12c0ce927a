<?php
defined('BASEPATH') or exit('No direct script access allowed');

$route['default_controller'] = 'home';
$route['404_override'] = 'error';
$route['translate_uri_dashes'] = false;

## captcha controller ##
$route['captcha'] = 'captcha';

## contact controller ##
#$route['contact'] = 'contact';

## error controller ##
$route['error'] = 'error';

## autologin controller ##
$route['autologin'] = 'autologin';

## asset files controller ##
$route['file/(.*)'] = 'file/index';

## healthcheck controller ##
$route['healthcheck'] = 'healthcheck/check';

## unsubscribe controller ##
$route['unsubscribe'] = 'unsubscribe/index';
$route['unsubscribe/remove'] = 'unsubscribe/remove';

## moderator controller ##
$route['presenter'] = 'presenter';
$route['presenter/login'] = 'presenter/login';
$route['presenter/logout'] = 'presenter/logout';

## Vipanel V2 Controller ##
$route['vipanel/v2'] = 'vipanel_v2/vi_dashboard';
$route['vipanel/v2/livestreaming-setup'] = 'vipanel_v2/vi_livestraming_setup/index';
$route['vipanel/v2/livestreaming-setup/save'] = 'vipanel_v2/vi_livestraming_setup/save';

$route['vipanel/v2/registration-management'] = 'vipanel_v2/vi_registration_management/index';
$route['vipanel/v2/registration-management/save'] = 'vipanel_v2/vi_registration_management/save';
$route['vipanel/v2/registration-management/retrieve'] = 'vipanel_v2/vi_registration_management/retrieve';

$route['vipanel/v2/interactive-tools'] = 'vipanel_v2/vi_interactive_tools/index';
$route['vipanel/v2/interactive-tools/save'] = 'vipanel_v2/vi_interactive_tools/save';
$route['vipanel/v2/interactive-tools/list'] = 'vipanel_v2/vi_interactive_tools/list';
$route['vipanel/v2/interactive-tools/certificate/test'] = 'vipanel_v2/vi_interactive_tools/certificateTest';
$route['vipanel/v2/interactive-tools/certificate/font-preview'] = 'vipanel_v2/vi_interactive_tools/certificateFontPreview';
$route['vipanel/v2/interactive-tools/survey/test'] = 'vipanel_v2/vi_interactive_tools/surveyTest';
$route['vipanel/v2/interactive-tools/polling/result'] = 'vipanel_v2/vi_interactive_tools/pollingResult';
$route['vipanel/v2/interactive-tools/topic/getComments'] = 'vipanel_v2/vi_interactive_tools/getComments';

$route['vipanel/v2/live-webinar-reports'] = 'vipanel_v2/vi_live_webinar_reports/index';
$route['vipanel/v2/live-webinar-reports/update-title'] = 'vipanel_v2/vi_live_webinar_reports/updateTitle';
$route['vipanel/v2/live-webinar-reports/fetch'] = 'vipanel_v2/vi_live_webinar_reports/fetch';

$route['vipanel/v2/on-demand-video'] = 'vipanel_v2/Vi_ondemand_video/index';
$route['vipanel/v2/on-demand-video/save'] = 'vipanel_v2/Vi_ondemand_video/save';
$route['vipanel/v2/on-demand-video/report'] = 'vipanel_v2/Vi_ondemand_video/report';

$route['vipanel/v2/transactional-emails'] = 'vipanel_v2/vi_transactional_emails/index';
$route['vipanel/v2/transactional-emails/create'] = 'vipanel_v2/vi_transactional_emails/create';
$route['vipanel/v2/transactional-emails/(:any)/edit'] = 'vipanel_v2/vi_transactional_emails/edit/$1';
$route['vipanel/v2/transactional-emails/(:any)/statistic'] = 'vipanel_v2/vi_transactional_emails/statistic/$1';
$route['vipanel/v2/transactional-emails/contacts'] = 'vipanel_v2/vi_transactional_emails/contacts';
$route['vipanel/v2/transactional-emails/contacts/unsubscribe'] = 'vipanel_v2/vi_transactional_emails/unsubscribe';
$route['vipanel/v2/transactional-emails/contacts/(:any)/preview'] = 'vipanel_v2/vi_transactional_emails/contactPreview/$1';
$route['vipanel/v2/transactional-emails/contacts/(:any)/add'] = 'vipanel_v2/vi_transactional_emails/contactManagement/$1';
$route['vipanel/v2/transactional-emails/contacts/(:any)/remove'] = 'vipanel_v2/vi_transactional_emails/contactManagement/$1';
$route['vipanel/v2/transactional-emails/make/(:any)'] = 'vipanel_v2/vi_transactional_emails/make/$1';
$route['vipanel/v2/transactional-emails/check/(:any)'] = 'vipanel_v2/vi_transactional_emails/check/$1';
$route['vipanel/v2/transactional-emails/template-html'] = 'vipanel_v2/vi_transactional_emails/ajaxEmailTemplateHtml';

$route['vipanel/v2/admin-settings/on-demand-video'] = 'vipanel_v2/vi_admin_settings/onDemandVideo';
$route['vipanel/v2/admin-settings/reports/tracked-time/detail'] = 'vipanel_v2/vi_admin_settings/trackedTimeDetail';
$route['vipanel/v2/admin-settings/reports/(:any)'] = 'vipanel_v2/vi_admin_settings/getReport/$1';
$route['vipanel/v2/admin-settings/reports'] = 'vipanel_v2/vi_admin_settings/getReport';
$route['vipanel/v2/admin-settings/site-users'] = 'vipanel_v2/vi_admin_settings/siteUsers';
$route['vipanel/v2/admin-settings/custom-panel-users'] = 'vipanel_v2/vi_admin_settings/panelUsers';
$route['vipanel/v2/admin-settings/afme-users'] = 'vipanel_v2/vi_admin_settings/afmeUsers';
$route['vipanel/v2/admin-settings/afme-users-ajax'] = 'vipanel_v2/vi_admin_settings/afmeUsersAjax';
$route['vipanel/v2/admin-settings/afme-users-import'] = 'vipanel_v2/vi_import/afmeUsersImport';
$route['vipanel/v2/admin-settings/save'] = 'vipanel_v2/vi_admin_settings/saveAdminSettings';
$route['vipanel/v2/admin-settings/vipanel_config'] = 'vipanel_v2/vi_admin_settings/vipanelConfig';
$route['vipanel/v2/admin-settings/recording_time_logger'] = 'vipanel_v2/vi_admin_settings/recordingTimeLogger';


$route['vipanel/v2/update-config'] = 'vipanel_v2/vi_dashboard/updateConfig';
$route['vipanel/v2/update-configs'] = 'vipanel_v2/vi_dashboard/updateConfigs';
$route['vipanel/v2/update-config-array'] = 'vipanel_v2/vi_dashboard/updateConfigArray';
$route['vipanel/v2/delete-config'] = 'vipanel_v2/vi_dashboard/deleteConfig';

## Datalake ##
$route['vipanel/v2/send/datalake'] = 'vipanel_v2/vi_datalake/sendData';

$route['vipanel/v2/change-theme'] = 'vipanel_v2/vi_auth/changeTheme';
$route['vipanel/v2/login'] = 'vipanel_v2/vi_auth/login';
$route['vipanel/v2/logout'] = 'vipanel_v2/vi_auth/logout';

## Vipanel Controller ##
$route['vipanel'] = 'vipanel/index';
$route['vipanel/login'] = 'vipanel/login';
$route['vipanel/logout'] = 'vipanel/logout';
$route['vipanel/(.*)'] = 'vipanel/index';

## manager controller ##
$route['moderator'] = 'moderator';
$route['moderator/login'] = 'moderator/login';
$route['moderator/logout'] = 'moderator/logout';
$route['moderator/chat'] = 'moderator/chat';
$route['moderator/embed'] = 'moderator/embed';

## Polling ##
$route['pollresult'] = 'polling/result';
$route['pollresult/pdf'] = 'polling/pdf';
$route['pollresult/ajax'] = 'polling/resultAjax';
$route['pollmanage'] = 'polling/manage';

## PFIZER ROUTES ##
$route['pfizer/autologin'] = 'pfizer/autologin';

## Ajax Controller ##
$route['ajax/post/next_page'] = 'ajax/post/next_page';
$route['ajax/post/prev_page'] = 'ajax/post/prev_page';
$route['ajax/post/login'] = 'ajax/post/login';
$route['ajax/post/check_user'] = 'ajax/post/check_user';
$route['ajax/post/check_config'] = 'ajax/post/check_config';
$route['ajax/post/check_configs'] = 'ajax/post/check_configs';
$route['ajax/post/check_vipanel_decline'] = 'ajax/post/check_vipanel_decline';
$route['ajax/post/tracked_time'] = 'ajax/post/tracked_time';
$route['ajax/post/recording_time'] = 'ajax/post/recording_time';
$route['ajax/post/recording_time/update-title'] = 'ajax/post/recording_time_update_title';
$route['ajax/post/set_custom_data'] = 'ajax/post/set_custom_data';
$route['ajax/post/set_user_data'] = 'ajax/post/set_user_data';
$route['ajax/post/get_hospitals'] = 'ajax/post/get_hospitals';
$route['ajax/post/get_specialities'] = 'ajax/post/get_specialities';
$route['ajax/post/get_announcement'] = 'ajax/post/get_announcement';
$route['ajax/post/check_info'] = 'ajax/post/check_info';
$route['ajax/post/elapsed_time'] = 'ajax/post/elapsed_time';
$route['ajax/post/end_date'] = 'ajax/post/end_date';
$route['ajax/post/change_language'] = 'ajax/post/change_language';
$route['ajax/post/change_stream_language'] = 'ajax/post/change_stream_language';
$route['ajax/post/set_audience'] = 'ajax/post/set_audience';
$route['ajax/post/contact'] = 'ajax/post/contact';
$route['ajax/post/survey'] = 'ajax/post/survey';
$route['ajax/post/quiz'] = 'ajax/post/quiz';
$route['ajax/post/change_embed'] = 'ajax/post/change_embed';
$route['ajax/post/check_embed'] = 'ajax/post/check_embed';
$route['ajax/post/register_check'] = 'ajax/post/register_check';
$route['ajax/post/register'] = 'ajax/post/register';
$route['ajax/post/cookie_consent'] = 'ajax/post/cookie_consent';
$route['ajax/post/old_messages'] = 'ajax/post/old_messages';
$route['ajax/post/check_moderator_password'] = 'ajax/post/check_moderator_password';
$route['ajax/post/get_polling'] = 'ajax/post/get_polling';
$route['ajax/post/set_polling_answer'] = 'ajax/post/set_polling_answer';
$route['ajax/post/check_survey_status'] = 'ajax/post/check_survey_status';
$route['ajax/post/update_current_time'] = 'ajax/post/update_current_time';
$route['ajax/post/get_current_time'] = 'ajax/post/get_current_time';
$route['ajax/post/get_messages'] = 'ajax/post/get_messages';
$route['ajax/post/get_ajax'] = 'ajax/post/getAjax';
$route['ajax/post/get_quiz'] = 'ajax/post/getQuiz';
$route['ajax/post/answer_quiz'] = 'ajax/post/answer_quiz';
$route['ajax/post/type_message'] = 'ajax/post/type_message';
$route['ajax/post/update_embed_log'] = 'ajax/post/update_embed_log';
$route['ajax/post/event_log'] = 'ajax/post/event_log';
$route['ajax/post/upload_image'] = 'ajax/post/upload_image';
$route['ajax/post/set_code'] = 'ajax/post/set_code';
$route['ajax/post/moderator_messages'] = 'ajax/post/moderator_messages';
$route['ajax/post/read_messages'] = 'ajax/post/read_messages';
$route['ajax/post/send_mail_ajax'] = 'ajax/post/send_mail_ajax';
$route['ajax/post/forgot_password'] = 'ajax/post/forgot_password';
$route['ajax/post/reset_password'] = 'ajax/post/reset_password';
$route['ajax/post/custom_users_continue'] = 'ajax/post/custom_users_continue';
$route['ajax/post/get_videos'] = 'ajax/post/get_videos';
$route['ajax/post/get_video'] = 'ajax/post/get_video';
$route['ajax/post/update_notification'] = 'ajax/post/update_notification';
$route['ajax/post/error_log'] = 'ajax/post/error_log';

# Pusher Auth !!
$route['ajax/pusher/auth'] = 'ajax/pusherauth/index';

# Messages Ajax
$route['ajax/message/user_old_messages'] = 'ajax/message/user_old_messages';
$route['ajax/message/presenter_old_messages'] = 'ajax/message/presenter_old_messages';
$route['ajax/message/moderator_old_messages'] = 'ajax/message/moderator_old_messages';
$route['ajax/message/save_user_chat_message'] = 'ajax/message/save_user_chat_message';
$route['ajax/message/save_presenter_chat_message'] = 'ajax/message/save_presenter_chat_message';
$route['ajax/message/save_moderator_chat_message'] = 'ajax/message/save_moderator_chat_message';
$route['ajax/message/update_read_status'] = 'ajax/message/update_read_status';
$route['ajax/message/update_visible_status'] = 'ajax/message/update_visible_status';
$route['ajax/message/delete_chat_message'] = 'ajax/message/delete_chat_message';
$route['ajax/message/update_chat_message'] = 'ajax/message/update_chat_message';

## Translator ##
$route['ajax/translator'] = 'ajax/translator/index';

# Retrieve Ajax
$route['ajax/retrieve/public-messages'] = 'ajax/retrieve/publicMessage';

## Logout Controller ##
$route['logout'] = 'logout';
$route['clear'] = 'logout'; //bunu site içerisinde kullanmadık test ekibi kullansın diye

$route['import/import-register-data-from-csv'] = 'Import/importRegisterDataFromCSV';

## Admin ##
$route['vistreampanel'] = 'vistreampanel/vi_home';
$route['vistreampanel/login'] = 'vistreampanel/vi_login/index';
$route['vistreampanel/logout'] = 'vistreampanel/vi_login/logout';
$route['vistreampanel/embed'] = 'vistreampanel/vi_embed/index';
$route['vistreampanel/embed/edit'] = 'vistreampanel/vi_embed/edit';
$route['vistreampanel/embed/edit/(:num)'] = 'vistreampanel/vi_embed/edit';
$route['vistreampanel/embed/create'] = 'vistreampanel/vi_embed/create';
$route['vistreampanel/embed/delete/(:num)'] = 'vistreampanel/vi_embed/delete/(:num)';
$route['vistreampanel/embed/check'] = 'vistreampanel/vi_embed/check';
$route['vistreampanel/embed/deletebyid'] = 'vistreampanel/vi_embed/deletebyid';

$route['vistreampanel/settings/image'] = 'vistreampanel/vi_image/index';
$route['vistreampanel/settings/image/do_upload'] = 'vistreampanel/vi_image/do_upload';
$route['vistreampanel/settings/image/remove/(:any)'] = 'vistreampanel/vi_image/remove/$1';

$route['vistreampanel/settings'] = 'vistreampanel/vi_settings/index';
$route['vistreampanel/settings/update-specialities'] = 'vistreampanel/vi_settings/updateSpecialities';
$route['vistreampanel/settings/update-countries'] = 'vistreampanel/vi_settings/updateCountries';
$route['vistreampanel/specialities'] = 'vistreampanel/vi_settings/specialities';

$route['vistreampanel/report'] = 'vistreampanel/vi_report/index';
$route['vistreampanel/report/user'] = 'vistreampanel/vi_report/user';
$route['vistreampanel/report/user/login-data'] = 'vistreampanel/vi_report/loginData';
$route['vistreampanel/report/user/permission-data'] = 'vistreampanel/vi_report/permissionData';
$route['vistreampanel/report/user/all-data'] = 'vistreampanel/vi_report/allData';
$route['vistreampanel/report/chat'] = 'vistreampanel/vi_report/chat';
$route['vistreampanel/report/survey'] = 'vistreampanel/vi_report/survey';
$route['vistreampanel/report/register'] = 'vistreampanel/vi_report/register';
$route['vistreampanel/report/embed'] = 'vistreampanel/vi_report/embed';
$route['vistreampanel/report/event'] = 'vistreampanel/vi_report/event';
$route['vistreampanel/report/polling'] = 'vistreampanel/vi_report/polling';
$route['vistreampanel/report/certificate'] = 'vistreampanel/vi_report/certificate';
$route['vistreampanel/report/tracked-time'] = 'vistreampanel/vi_report/tracked_time';
$route['vistreampanel/report/tracked-time/detail'] = 'vistreampanel/vi_report/tracked_time_detail';

$route['vistreampanel/scp-control'] = 'vistreampanel/vi_scp/index';

$route['vistreampanel/users'] = 'vistreampanel/vi_users/index';
$route['vistreampanel/user_create'] = 'vistreampanel/vi_users/user_create';
$route['vistreampanel/user_delete/(:num)'] = 'vistreampanel/vi_users/user_delete/$1';
$route['vistreampanel/user_edit/(:num)'] = 'vistreampanel/vi_users/edit/$1';

$route['vistreampanel/panel-users'] = 'vistreampanel/vi_panel_users/index';
$route['vistreampanel/panel-users/user_create'] = 'vistreampanel/vi_panel_users/user_create';
$route['vistreampanel/panel-users/user_delete/(:num)'] = 'vistreampanel/vi_panel_users/user_delete/$1';
$route['vistreampanel/panel-users/user_edit/(:num)'] = 'vistreampanel/vi_panel_users/edit/$1';

$route['vistreampanel/video'] = 'vistreampanel/vi_video/index';
$route['vistreampanel/video/edit'] = 'vistreampanel/vi_video/edit';
$route['vistreampanel/video/edit/(:num)'] = 'vistreampanel/vi_video/edit';
$route['vistreampanel/video/create'] = 'vistreampanel/vi_video/create';
$route['vistreampanel/video/delete/(:num)'] = 'vistreampanel/vi_video/delete/(:num)';
$route['vistreampanel/video/delete_image'] = 'vistreampanel/vi_video/delete_image';

$route['vistreampanel/ondemandvideo'] = 'vistreampanel/vi_ondemandvideo/index';
$route['vistreampanel/ondemandvideo/report'] = 'vistreampanel/vi_ondemandvideo/report';
$route['vistreampanel/ondemandvideo/edit'] = 'vistreampanel/vi_ondemandvideo/edit';
$route['vistreampanel/ondemandvideo/edit/(:num)'] = 'vistreampanel/vi_ondemandvideo/edit';
$route['vistreampanel/ondemandvideo/create'] = 'vistreampanel/vi_ondemandvideo/create';
$route['vistreampanel/ondemandvideo/delete/(:num)'] = 'vistreampanel/vi_ondemandvideo/delete/(:num)';
$route['vistreampanel/ondemandvideo/delete_image'] = 'vistreampanel/vi_ondemandvideo/delete_image';

$route['vistreampanel/quiz'] = 'vistreampanel/vi_quiz/index';
$route['vistreampanel/quiz/edit'] = 'vistreampanel/vi_quiz/editQuestion';
$route['vistreampanel/quiz/edit/(:num)'] = 'vistreampanel/vi_quiz/editQuestion/$1';
$route['vistreampanel/quiz/create'] = 'vistreampanel/vi_quiz/createQuestion';
$route['vistreampanel/quiz/delete/(:num)'] = 'vistreampanel/vi_quiz/deleteQuestion/$1';
$route['vistreampanel/quiz/report'] = 'vistreampanel/vi_quiz/report';

$route['vistreampanel/survey'] = 'vistreampanel/vi_survey/index';
$route['vistreampanel/survey/edit'] = 'vistreampanel/vi_survey/editQuestion';
$route['vistreampanel/survey/edit/(:num)'] = 'vistreampanel/vi_survey/editQuestion/$1';
$route['vistreampanel/survey/create'] = 'vistreampanel/vi_survey/createQuestion';
$route['vistreampanel/survey/delete/(:num)'] = 'vistreampanel/vi_survey/deleteQuestion/$1';
$route['vistreampanel/survey/report'] = 'vistreampanel/vi_survey/report';

$route['vistreampanel/polling'] = 'vistreampanel/vi_polling/index';
$route['vistreampanel/polling/create'] = 'vistreampanel/vi_polling/createQuestion';
$route['vistreampanel/polling/edit/(:num)'] = 'vistreampanel/vi_polling/editQuestion/$1';
$route['vistreampanel/polling/delete/(:num)'] = 'vistreampanel/vi_polling/deleteQuestion/$1';
$route['vistreampanel/polling/delete_votes/(:num)'] = 'vistreampanel/vi_polling/deleteVotes/$1';
$route['vistreampanel/polling/report/(:num)'] = 'vistreampanel/vi_polling/report/$1';

$route['vistreampanel/afme_users'] = 'vistreampanel/vi_import/index';
$route['vistreampanel/afme_users/ajax'] = 'vistreampanel/vi_import/afme_users';
$route['vistreampanel/afme_users/import'] = 'vistreampanel/vi_import/import_afme_users';

$route['vistreampanel/import_register'] = 'vistreampanel/vi_import/register';
$route['vistreampanel/import_register/import'] = 'vistreampanel/vi_import/import_register';

## vistreampanel Meeting Management
$route['vistreampanel/meeting'] = 'vistreampanel/vi_meeting/index';

## Special pages
$route['vistreampanel/event-definitions'] = 'vistreampanel/vi_other/eventDefinitions';
$route['vistreampanel/event-definitions/excel'] = 'vistreampanel/vi_other/eventDefinitionsExcel';

## vistreampanel AJAX
$route['vistreampanel/ajax/change_embed_order'] = 'vistreampanel/ajax/vi_ajax/change_embed_order';
$route['vistreampanel/ajax/change_video_order'] = 'vistreampanel/ajax/vi_ajax/change_video_order';
$route['vistreampanel/ajax/change_polling_question_order'] = 'vistreampanel/ajax/vi_ajax/change_polling_question_order';
$route['vistreampanel/ajax/change_polling_answer_order'] = 'vistreampanel/ajax/vi_ajax/change_polling_answer_order';
$route['vistreampanel/ajax/delete_quiz_option'] = 'vistreampanel/ajax/vi_ajax/delete_quiz_option';
$route['vistreampanel/ajax/delete_survey_option'] = 'vistreampanel/ajax/vi_ajax/delete_survey_option';
$route['vistreampanel/ajax/delete_polling_answer'] = 'vistreampanel/ajax/vi_ajax/delete_polling_answer';
$route['vistreampanel/ajax/meeting_add_user'] = 'vistreampanel/ajax/vi_ajax/meeting_add_user';
$route['vistreampanel/ajax/meeting_remove_user'] = 'vistreampanel/ajax/vi_ajax/meeting_remove_user';
$route['vistreampanel/ajax/meeting_save_url'] = 'vistreampanel/ajax/vi_ajax/meeting_save_url';
$route['vistreampanel/ajax/config'] = 'vistreampanel/vi_settings/config';

$route['vistreampanel/log/error'] = 'vistreampanel/vi_log/errorLog';


## Admin V2 ##
$route['vistreampanel'] = 'vistreampanel_v2/vi_home';
$route['vistreampanel/v2/login'] = 'vistreampanel_v2/vi_login/index';
$route['vistreampanel/v2/logout'] = 'vistreampanel_v2/vi_login/logout';
$route['vistreampanel/v2/embed'] = 'vistreampanel_v2/vi_embed/index';
$route['vistreampanel/v2/embed/edit'] = 'vistreampanel_v2/vi_embed/edit';
$route['vistreampanel/v2/embed/edit/(:num)'] = 'vistreampanel_v2/vi_embed/edit';
$route['vistreampanel/v2/embed/create'] = 'vistreampanel_v2/vi_embed/create';
$route['vistreampanel/v2/embed/delete/(:num)'] = 'vistreampanel_v2/vi_embed/delete/(:num)';
$route['vistreampanel/v2/embed/check'] = 'vistreampanel_v2/vi_embed/check';
$route['vistreampanel/v2/embed/deletebyid'] = 'vistreampanel_v2/vi_embed/deletebyid';

$route['vistreampanel/v2/settings/image'] = 'vistreampanel_v2/vi_image/index';
$route['vistreampanel/v2/settings/image/do_upload'] = 'vistreampanel_v2/vi_image/do_upload';
$route['vistreampanel/v2/settings/image/remove/(:any)'] = 'vistreampanel_v2/vi_image/remove/$1';

$route['vistreampanel/v2/settings'] = 'vistreampanel_v2/vi_settings/index';
$route['vistreampanel/v2/settings/update-config'] = 'vistreampanel_v2/vi_settings/updateConfig';
$route['vistreampanel/v2/settings/update-specialities'] = 'vistreampanel_v2/vi_settings/updateSpecialities';
$route['vistreampanel/v2/settings/update-countries'] = 'vistreampanel_v2/vi_settings/updateCountries';
$route['vistreampanel/v2/settings/vipanel'] = 'vistreampanel_v2/vi_vipanel/index';
$route['vistreampanel/v2/settings/vipanel/config'] = 'vistreampanel_v2/vi_vipanel/config';
$route['vistreampanel/v2/settings/vipanel/config/save'] = 'vistreampanel_v2/vi_vipanel/configSave';
$route['vistreampanel/v2/settings/vipanel/create-user'] = 'vistreampanel_v2/vi_vipanel/createUser';
$route['vistreampanel/v2/settings/vipanel/delete-user'] = 'vistreampanel_v2/vi_vipanel/deleteUser';
$route['vistreampanel/v2/settings/vipanel/edit-user'] = 'vistreampanel_v2/vi_vipanel/editUser';
$route['vistreampanel/v2/specialities'] = 'vistreampanel_v2/vi_settings/specialities';
$route['vistreampanel/v2/settings/metadata'] = 'vistreampanel_v2/vi_settings/metadata';
$route['vistreampanel/v2/settings/metadata/edit/(:num)'] = 'vistreampanel_v2/vi_settings/metadataEdit';
$route['vistreampanel/v2/settings/metadata/delete/(:num)'] = 'vistreampanel_v2/vi_settings/metadataDelete';
$route['vistreampanel/v2/settings/metadata/create'] = 'vistreampanel_v2/vi_settings/metadataCreate';

$route['vistreampanel/v2/report'] = 'vistreampanel_v2/vi_report/index';
$route['vistreampanel/v2/report/user'] = 'vistreampanel_v2/vi_report/user';
$route['vistreampanel/v2/report/user/login-data'] = 'vistreampanel_v2/vi_report/loginData';
$route['vistreampanel/v2/report/user/permission-data'] = 'vistreampanel_v2/vi_report/permissionData';
$route['vistreampanel/v2/report/user/all-data'] = 'vistreampanel_v2/vi_report/allData';
$route['vistreampanel/v2/report/chat'] = 'vistreampanel_v2/vi_report/chat';
$route['vistreampanel/v2/report/survey'] = 'vistreampanel_v2/vi_report/survey';
$route['vistreampanel/v2/report/register'] = 'vistreampanel_v2/vi_report/register';
$route['vistreampanel/v2/report/embed'] = 'vistreampanel_v2/vi_report/embed';
$route['vistreampanel/v2/report/event'] = 'vistreampanel_v2/vi_report/event';
$route['vistreampanel/v2/report/polling'] = 'vistreampanel_v2/vi_report/polling';
$route['vistreampanel/v2/report/certificate'] = 'vistreampanel_v2/vi_report/certificate';
$route['vistreampanel/v2/report/tracked-time'] = 'vistreampanel_v2/vi_report/tracked_time';
$route['vistreampanel/v2/report/tracked-time/detail'] = 'vistreampanel_v2/vi_report/tracked_time_detail';

$route['vistreampanel/v2/report/get-data/(:any)'] = 'vistreampanel_v2/vi_report/getData';
$route['vistreampanel/v2/reports/(:any)'] = 'vistreampanel_v2/vi_report/main';

// $route['vistreampanel/v2/scp-control'] = 'vistreampanel_v2/vi_scp/index';

$route['vistreampanel/v2/users'] = 'vistreampanel_v2/vi_users/index';
$route['vistreampanel/v2/user_create'] = 'vistreampanel_v2/vi_users/user_create';
$route['vistreampanel/v2/user_delete/(:num)'] = 'vistreampanel_v2/vi_users/user_delete/$1';
$route['vistreampanel/v2/user_edit/(:num)'] = 'vistreampanel_v2/vi_users/edit/$1';

$route['vistreampanel/v2/panel-users'] = 'vistreampanel_v2/vi_panel_users/index';
$route['vistreampanel/v2/panel-users/user_create'] = 'vistreampanel_v2/vi_panel_users/user_create';
$route['vistreampanel/v2/panel-users/user_delete/(:num)'] = 'vistreampanel_v2/vi_panel_users/user_delete/$1';
$route['vistreampanel/v2/panel-users/user_edit/(:num)'] = 'vistreampanel_v2/vi_panel_users/edit/$1';

$route['vistreampanel/v2/video'] = 'vistreampanel_v2/vi_video/index';
$route['vistreampanel/v2/video/edit'] = 'vistreampanel_v2/vi_video/edit';
$route['vistreampanel/v2/video/edit/(:num)'] = 'vistreampanel_v2/vi_video/edit';
$route['vistreampanel/v2/video/create'] = 'vistreampanel_v2/vi_video/create';
$route['vistreampanel/v2/video/delete/(:num)'] = 'vistreampanel_v2/vi_video/delete/(:num)';
$route['vistreampanel/v2/video/delete_image'] = 'vistreampanel_v2/vi_video/delete_image';

$route['vistreampanel/v2/ondemandvideo'] = 'vistreampanel_v2/vi_ondemandvideo/index';
$route['vistreampanel/v2/ondemandvideo/report'] = 'vistreampanel_v2/vi_ondemandvideo/report';
$route['vistreampanel/v2/ondemandvideo/edit'] = 'vistreampanel_v2/vi_ondemandvideo/edit';
$route['vistreampanel/v2/ondemandvideo/edit/(:num)'] = 'vistreampanel_v2/vi_ondemandvideo/edit';
$route['vistreampanel/v2/ondemandvideo/create'] = 'vistreampanel_v2/vi_ondemandvideo/create';
$route['vistreampanel/v2/ondemandvideo/delete/(:num)'] = 'vistreampanel_v2/vi_ondemandvideo/delete/(:num)';
$route['vistreampanel/v2/ondemandvideo/delete_image'] = 'vistreampanel_v2/vi_ondemandvideo/delete_image';

$route['vistreampanel/v2/quiz'] = 'vistreampanel_v2/vi_quiz/index';
$route['vistreampanel/v2/quiz/edit'] = 'vistreampanel_v2/vi_quiz/editQuestion';
$route['vistreampanel/v2/quiz/edit/(:num)'] = 'vistreampanel_v2/vi_quiz/editQuestion/$1';
$route['vistreampanel/v2/quiz/create'] = 'vistreampanel_v2/vi_quiz/createQuestion';
$route['vistreampanel/v2/quiz/delete/(:num)'] = 'vistreampanel_v2/vi_quiz/deleteQuestion/$1';
$route['vistreampanel/v2/quiz/report'] = 'vistreampanel_v2/vi_quiz/report';

$route['vistreampanel/v2/survey'] = 'vistreampanel_v2/vi_survey/index';
$route['vistreampanel/v2/survey/edit'] = 'vistreampanel_v2/vi_survey/editQuestion';
$route['vistreampanel/v2/survey/edit/(:num)'] = 'vistreampanel_v2/vi_survey/editQuestion/$1';
$route['vistreampanel/v2/survey/create'] = 'vistreampanel_v2/vi_survey/createQuestion';
$route['vistreampanel/v2/survey/delete/(:num)'] = 'vistreampanel_v2/vi_survey/deleteQuestion/$1';
$route['vistreampanel/v2/survey/report'] = 'vistreampanel_v2/vi_survey/report';

$route['vistreampanel/v2/polling'] = 'vistreampanel_v2/vi_polling/index';
$route['vistreampanel/v2/polling/create'] = 'vistreampanel_v2/vi_polling/createQuestion';
$route['vistreampanel/v2/polling/edit/(:num)'] = 'vistreampanel_v2/vi_polling/editQuestion/$1';
$route['vistreampanel/v2/polling/delete/(:num)'] = 'vistreampanel_v2/vi_polling/deleteQuestion/$1';
$route['vistreampanel/v2/polling/delete_votes/(:num)'] = 'vistreampanel_v2/vi_polling/deleteVotes/$1';
$route['vistreampanel/v2/polling/report/(:num)'] = 'vistreampanel_v2/vi_polling/report/$1';

$route['vistreampanel/v2/afme_users'] = 'vistreampanel_v2/vi_import/index';
$route['vistreampanel/v2/afme_users/ajax'] = 'vistreampanel_v2/vi_import/afme_users';
$route['vistreampanel/v2/afme_users/import'] = 'vistreampanel_v2/vi_import/import_afme_users';

$route['vistreampanel/v2/import_register'] = 'vistreampanel_v2/vi_import/register';
$route['vistreampanel/v2/import_register/import'] = 'vistreampanel_v2/vi_import/import_register';

## vistreampanel Meeting Management
$route['vistreampanel/v2/meeting'] = 'vistreampanel_v2/vi_meeting/index';

##Report Chart

$route['vistreampanel/v2/charts'] = 'vistreampanel_v2/vi_chart/index';


## Special pages
$route['vistreampanel/v2/event-definitions'] = 'vistreampanel_v2/vi_other/eventDefinitions';
$route['vistreampanel/v2/event-definitions/excel'] = 'vistreampanel_v2/vi_other/eventDefinitionsExcel';

## vistreampanel AJAX
$route['vistreampanel/v2/ajax/change_embed_order'] = 'vistreampanel_v2/ajax/vi_ajax/change_embed_order';
$route['vistreampanel/v2/ajax/change_video_order'] = 'vistreampanel_v2/ajax/vi_ajax/change_video_order';
$route['vistreampanel/v2/ajax/change_polling_question_order'] = 'vistreampanel_v2/ajax/vi_ajax/change_polling_question_order';
$route['vistreampanel/v2/ajax/change_polling_answer_order'] = 'vistreampanel_v2/ajax/vi_ajax/change_polling_answer_order';
$route['vistreampanel/v2/ajax/delete_quiz_option'] = 'vistreampanel_v2/ajax/vi_ajax/delete_quiz_option';
$route['vistreampanel/v2/ajax/delete_survey_option'] = 'vistreampanel_v2/ajax/vi_ajax/delete_survey_option';
$route['vistreampanel/v2/ajax/delete_polling_answer'] = 'vistreampanel_v2/ajax/vi_ajax/delete_polling_answer';
$route['vistreampanel/v2/ajax/meeting_add_user'] = 'vistreampanel_v2/ajax/vi_ajax/meeting_add_user';
$route['vistreampanel/v2/ajax/meeting_remove_user'] = 'vistreampanel_v2/ajax/vi_ajax/meeting_remove_user';
$route['vistreampanel/v2/ajax/meeting_save_url'] = 'vistreampanel_v2/ajax/vi_ajax/meeting_save_url';
$route['vistreampanel/v2/ajax/config'] = 'vistreampanel_v2/vi_settings/config';

$route['vistreampanel/v2/log/error'] = 'vistreampanel_v2/vi_log/errorLog';

$route['vistreampanel/(:any)'] = 'vistreampanel_v2/vi_home';
$route['vistreampanel/(:any)/(:any)'] = 'vistreampanel_v2/vi_home';

$route['command/(:any)'] = 'Command/index/$1';
$route['command/(:any)/(:any)'] = 'Command/withValue/$1/$2';

$route['robots.txt'] = 'CustomPages/robots';

$route['callback/(:any)'] = 'Callback/index/$1';

## Redirect all request to home controller ##
$route['(.*)'] = 'home';
