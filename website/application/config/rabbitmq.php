<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/*
|--------------------------------------------------------------------------
| RabbitMQ Configuration
|--------------------------------------------------------------------------
|
| This file contains the configuration settings for connecting to RabbitMQ.
|
*/

$config['rabbitmq'] = array(
    'host'     => ENV_RABBITMQ_HOST,
    'port'     => ENV_RABBITMQ_PORT,
    'user'     => ENV_RABBITMQ_USER,
    'password' => ENV_RABBITMQ_PASS,
    'vhost'    => ENV_RABBITMQ_VHOST
);

// Default exchange settings
$config['exchange'] = array(
    'name'        => 'vistream_exchange',
    'type'        => 'direct',
    'passive'     => false,
    'durable'     => true,
    'auto_delete' => false
);

// Default queue settings
$config['queue'] = array(
    'passive'     => false,
    'durable'     => true,
    'exclusive'   => false,
    'auto_delete' => false
);

// Define common queues used in the application
$config['queues'] = array(
    'default'     => 'vistream_default',
    'emails'      => 'vistream_emails',
    'notifications' => 'vistream_notifications',
    'logs'        => 'vistream_logs'
);
