<?php
$shortcuts = [
    'livestreaming' => [
        [
            'title' => 'Stream Embed',
            'subtitle' => 'Livestreaming Setup',
            'link' => '/vipanel/v2/livestreaming-setup?type=stream_embed',
            'bg_color' => 'bg-light-primary',
            'text_color' => 'text-primary',
            'permission' => $permissions['livestreaming_setup']['stream_embed'] ?? false
        ],
        [
            'title' => 'Backstage URL Setup',
            'subtitle' => 'Livestreaming Setup',
            'link' => '/vipanel/v2/livestreaming-setup?type=backstage_setup',
            'bg_color' => 'bg-light-primary',
            'text_color' => 'text-primary',
            'permission' => $permissions['livestreaming_setup']['backstage_setup'] ?? false
        ]
    ],

    'registration' => [
        [
            'title' => 'Registration List',
            'subtitle' => 'Registration Management',
            'link' => '/vipanel/v2/registration-management?type=registration_list',
            'bg_color' => 'bg-light-warning',
            'text_color' => 'text-warning',
            'permission' => $permissions['registration_management']['registration_list'] ?? false
        ],
        [
            'title' => 'Register Management',
            'subtitle' => 'Registration Management',
            'link' => '/vipanel/v2/registration-management?type=register_management',
            'bg_color' => 'bg-light-warning',
            'text_color' => 'text-warning',
            'permission' => $permissions['registration_management']['register_management'] ?? false
        ],
        [
            'title' => 'P. Reg. Email Settings',
            'subtitle' => 'Registration Management',
            'link' => '/vipanel/v2/registration-management?type=p_reg_email_settings',
            'bg_color' => 'bg-light-warning',
            'text_color' => 'text-warning',
            'permission' => $permissions['registration_management']['p_reg_email_settings'] ?? false
        ],
        [
            'title' => 'Access Restrictions',
            'subtitle' => 'Registration Management',
            'link' => '/vipanel/v2/registration-management?type=access_restrictions',
            'bg_color' => 'bg-light-warning',
            'text_color' => 'text-warning',
            'permission' => $permissions['registration_management']['access_restrictions'] ?? false
        ],
        [
            'title' => 'Login Settings',
            'subtitle' => 'Registration Management',
            'link' => '/vipanel/v2/registration-management?type=login_settings',
            'bg_color' => 'bg-light-warning',
            'text_color' => 'text-warning',
            'permission' => $permissions['registration_management']['login_settings'] ?? false
        ],
        [
            'title' => 'Timezone Settings',
            'subtitle' => 'Registration Management',
            'link' => '/vipanel/v2/registration-management?type=timezone_settings',
            'bg_color' => 'bg-light-warning',
            'text_color' => 'text-warning',
            'permission' => $permissions['registration_management']['timezone_settings'] ?? false
        ]
    ],

    'interactive' => [
        [
            'title' => 'Q&A Modules',
            'subtitle' => 'Interactive Tools',
            'link' => '/vipanel/v2/interactive-tools?type=qa_modules',
            'bg_color' => 'bg-light-success',
            'text_color' => 'text-success',
            'permission' => $permissions['interactive_tools']['qa_modules'] ?? false
        ],
        [
            'title' => 'Polling Modules',
            'subtitle' => 'Interactive Tools',
            'link' => '/vipanel/v2/interactive-tools?type=polling_modules',
            'bg_color' => 'bg-light-success',
            'text_color' => 'text-success',
            'permission' => $permissions['interactive_tools']['polling_modules'] ?? false
        ],
        [
            'title' => 'Pre-survey Setup',
            'subtitle' => 'Interactive Tools',
            'link' => '/vipanel/v2/interactive-tools?type=pre_survey_setup',
            'bg_color' => 'bg-light-success',
            'text_color' => 'text-success',
            'permission' => $permissions['interactive_tools']['pre_survey_setup'] ?? false
        ],
        [
            'title' => 'Post-survey Setup',
            'subtitle' => 'Interactive Tools',
            'link' => '/vipanel/v2/interactive-tools?type=post_survey_setup',
            'bg_color' => 'bg-light-success',
            'text_color' => 'text-success',
            'permission' => $permissions['interactive_tools']['post_survey_setup'] ?? false
        ],
        [
            'title' => 'Certificate Modules',
            'subtitle' => 'Interactive Tools',
            'link' => '/vipanel/v2/interactive-tools?type=certificate_modules',
            'bg_color' => 'bg-light-success',
            'text_color' => 'text-success',
            'permission' => $permissions['interactive_tools']['certificate_modules'] ?? false
        ],
        [
            'title' => 'Quiz Modules',
            'subtitle' => 'Interactive Tools',
            'link' => '/vipanel/v2/interactive-tools?type=quiz_modules',
            'bg_color' => 'bg-light-success',
            'text_color' => 'text-success',
            'permission' => $permissions['interactive_tools']['quiz_modules'] ?? false
        ],
        [
            'title' => 'Notifications',
            'subtitle' => 'Interactive Tools',
            'link' => '/vipanel/v2/interactive-tools?type=notifications',
            'bg_color' => 'bg-light-success',
            'text_color' => 'text-success',
            'permission' => $permissions['interactive_tools']['notifications'] ?? false
        ],
	    [
		    'title' => 'Announcements',
		    'subtitle' => 'Interactive Tools',
		    'link' => '/vipanel/v2/interactive-tools?type=announcements',
		    'bg_color' => 'bg-light-success',
		    'text_color' => 'text-success',
		    'permission' => $permissions['interactive_tools']['announcements'] ?? false
	    ],
	    [
		    'title' => 'Topic Management',
		    'subtitle' => 'Interactive Tools',
		    'link' => '/vipanel/v2/interactive-tools?type=topic_management',
		    'bg_color' => 'bg-light-success',
		    'text_color' => 'text-success',
		    'permission' => $permissions['interactive_tools']['topic_management'] ?? false
	    ]
    ],
    'livewebinar' => [
        [
            'title' => 'Attendee Report',
            'subtitle' => 'Live Webinar Reports',
            'link' => '/vipanel/v2/live-webinar-reports?type=attendee_report',
            'bg_color' => 'live-webinar-bg-color',
            'text_color' => 'live-webinar-color',
            'permission' => $permissions['live_webinar_reports']['attendee_report'] ?? false
        ],
        [
            'title' => 'Login Report',
            'subtitle' => 'Login Reports',
            'link' => '/vipanel/v2/live-webinar-reports?type=login_report',
            'bg_color' => 'live-webinar-bg-color',
            'text_color' => 'live-webinar-color',
            'permission' => $permissions['live_webinar_reports']['login_report'] ?? false
        ],
        [
            'title' => 'Chat Report',
            'subtitle' => 'Live Webinar Reports',
            'link' => '/vipanel/v2/live-webinar-reports?type=chat_report',
            'bg_color' => 'live-webinar-bg-color',
            'text_color' => 'live-webinar-color',
            'permission' => $permissions['live_webinar_reports']['chat_report'] ?? false
        ],
        [
            'title' => 'Polling Report',
            'subtitle' => 'Live Webinar Reports',
            'link' => '/vipanel/v2/live-webinar-reports?type=polling_report',
            'bg_color' => 'live-webinar-bg-color',
            'text_color' => 'live-webinar-color',
            'permission' => $permissions['live_webinar_reports']['polling_report'] ?? false
        ],
        [
            'title' => 'Survey Report',
            'subtitle' => 'Live Webinar Reports',
            'link' => '/vipanel/v2/live-webinar-reports?type=survey_report',
            'bg_color' => 'live-webinar-bg-color',
            'text_color' => 'live-webinar-color',
            'permission' => $permissions['live_webinar_reports']['survey_report'] ?? false
        ],
        [
            'title' => 'Reaction Report',
            'subtitle' => 'Live Webinar Reports',
            'link' => '/vipanel/v2/live-webinar-reports?type=reaction_report',
            'bg_color' => 'live-webinar-bg-color',
            'text_color' => 'live-webinar-color',
            'permission' => $permissions['live_webinar_reports']['reaction_report'] ?? false
        ],
        [
            'title' => 'Webinar Metrics & Insights',
            'subtitle' => 'Live Webinar Reports',
            'link' => '/vipanel/v2/live-webinar-reports?type=webinar_metrics_insights',
            'bg_color' => 'live-webinar-bg-color',
            'text_color' => 'live-webinar-color',
            'permission' => $permissions['live_webinar_reports']['webinar_metrics_insights'] ?? false
        ],
    ]
];
?>

<?php if(($permissions['livestreaming_setup'] ?? false) || ($permissions['registration_management'] ?? false) || ($permissions['interactive_tools'] ?? false) || ($permissions['live_webinar_reports'] ?? false)):  ?>
<div class="card w-100 min-h-350">
    <div class="card-body">
        <div class="mb-4">
            <h5 class="mb-0 fw-semibold">Shortcuts & Navigation</h5>
        </div>

        <div class="card-body p-4">
            <!-- Nav tabs -->
            <ul class="nav nav-tabs nav-fill" role="tablist">
                <?php if ($permissions['livestreaming_setup'] ?? false) : ?>
                    <li class="nav-item" role="presentation" id="livestreaming-tab">
                        <a class="nav-link active" data-bs-toggle="tab" href="#livestreaming" role="tab" aria-selected="false" tabindex="-1">
                            <span>Livestreaming Setup</span>
                        </a>
                    </li>
                <?php endif; ?>
                <?php if ($permissions['registration_management'] ?? false) : ?>
                    <li class="nav-item" role="presentation" id="registration-tab">
                        <a class="nav-link" data-bs-toggle="tab" href="#registration" role="tab" aria-selected="true">
                            <span>Registration Management</span>
                        </a>
                    </li>
                <?php endif; ?>
                <?php if ($permissions['interactive_tools'] ?? false) : ?>
                    <li class="nav-item" role="presentation" id="interactive-tab">
                        <a class="nav-link" data-bs-toggle="tab" href="#interactive" role="tab" aria-selected="true">
                            <span>Interactive Tools</span>
                        </a>
                    </li>
                <?php endif; ?>
                <?php if ($permissions['live_webinar_reports'] ?? false) : ?>
                    <li class="nav-item" role="presentation" id="live-webinar-tab">
                        <a class="nav-link" data-bs-toggle="tab" href="#livewebinar" role="tab" aria-selected="true">
                            <span>Live Webinar Reports</span>
                        </a>
                    </li>
                <?php endif; ?>
            </ul>
            <!-- Tab panes -->
            <div class="tab-content mt-4">
                <div class="tab-pane dashboard-tab-pane active show" id="livestreaming" role="tabpanel">
                    <div class="owl-carousel shortcut-carousel owl-theme" style="margin-bottom: -15px">
                        <?php foreach ($shortcuts['livestreaming'] as $i => $shortcut) : ?>
                            <?php if ($shortcut['permission']) : ?>
                                <a href="<?= $shortcut['link'] ?>" class="item">
                                    <div class="card border-0 zoom-in <?= $shortcut['bg_color'] ?> shadow-none">
                                        <div class="card-body px-2 mx-auto">
                                            <div class="text-center">
                                                <h6 class="fw-bolder <?= $shortcut['text_color'] ?>
                                                d-flex align-items-center" style="font-size: 12px; line-height: 1.5em; min-height:3em;">
                                                    <?= $shortcut['title'] ?>
                                                </h6>
                                            </div>
                                        </div>
                                    </div>
                                </a>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                </div>
                <div class="tab-pane dashboard-tab-pane active show" id="registration" role="tabpanel">
                    <div class="owl-carousel shortcut-carousel owl-theme" style="margin-bottom: -15px">
                        <?php foreach ($shortcuts['registration'] as $i => $shortcut) : ?>
                            <?php if ($shortcut['permission']) : ?>
                                <a href="<?= $shortcut['link'] ?>" class="item">
                                    <div class="card border-0 zoom-in <?= $shortcut['bg_color'] ?> shadow-none">
                                        <div class="card-body px-2 mx-auto">
                                            <div class="text-center">
                                                <h6 class="fw-bolder <?= $shortcut['text_color'] ?>
                                                d-flex align-items-center" style="font-size: 12px; line-height: 1.5em; min-height:3em;">
                                                    <?= $shortcut['title'] ?>
                                                </h6>
                                            </div>
                                        </div>
                                    </div>
                                </a>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                </div>
                <div class="tab-pane dashboard-tab-pane active show" id="interactive" role="tabpanel">
                    <div class="owl-carousel shortcut-carousel owl-theme" style="margin-bottom: -15px">
                        <?php foreach ($shortcuts['interactive'] as $i => $shortcut) : ?>
                            <?php if ($shortcut['permission']) : ?>
                                <a href="<?= $shortcut['link'] ?>" class="item">
                                    <div class="card border-0 zoom-in <?= $shortcut['bg_color'] ?> shadow-none">
                                        <div class="card-body px-2 mx-auto">
                                            <div class="text-center">
                                                <h6 class="fw-bolder <?= $shortcut['text_color'] ?>
                                                d-flex align-items-center" style="font-size: 12px; line-height: 1.5em; min-height:3em;">
                                                    <?= $shortcut['title'] ?>
                                                </h6>
                                            </div>
                                        </div>
                                    </div>
                                </a>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                </div>
                <div class="tab-pane dashboard-tab-pane active show" id="livewebinar" role="tabpanel">
                    <div class="owl-carousel shortcut-carousel owl-theme" style="margin-bottom: -15px">
                        <?php foreach ($shortcuts['livewebinar'] as $i => $shortcut) : ?>
                            <?php if ($shortcut['permission']) : ?>
                                <a href="<?= $shortcut['link'] ?>" class="item">
                                    <div class="card border-0 zoom-in <?= $shortcut['bg_color'] ?> shadow-none">
                                        <div class="card-body px-2 mx-auto">
                                            <div class="text-center">
                                                <h6 class="fw-bolder <?= $shortcut['text_color'] ?> 
                                                d-flex align-items-center" style="font-size: 12px; line-height: 1.5em; min-height:3em;">
                                                    <?= $shortcut['title'] ?>
                                                </h6>
                                            </div>
                                        </div>
                                    </div>
                                </a>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>
<?php endif; ?>

<div class="card w-100 bg-light-info overflow-hidden shadow-none">
    <div class="card-body py-3">
        <div class="row justify-content-between align-items-center">
            <div class="col-sm-6">
                <h5 class="fw-semibold mb-9 fs-5">Vistream Support & Development</h5>
                <p class="mb-9">
                    You can write your suggestions or errors related to the system to us
                </p>
                <a href="https://forms.clickup.com/9003115971/f/8ca19e3-10200/VHLERX3EB95A9LNNKD" target="_blank"
                    class="btn btn-primary">Contact us</a>
            </div>
            <div class="col-sm-5">
                <div class="position-relative text-end" style="margin-bottom: -16px">
                    <img src="/assets/vipanel_v2/dist/images/edited_welcome_review.png" alt="" class="img-fluid">
                </div>
            </div>
        </div>
    </div>
</div>

<script src="/assets/vipanel_v2/dist/libs/owl.carousel/dist/owl.carousel.min.js"></script>
<script>
    $(".shortcut-carousel").owlCarousel({
        loop: false,
        margin: 30,
        mouseDrag: true,
        nav: false,
        responsive: {
            0: {
                items: 2,
            },
            576: {
                items: 2,
            },
            768: {
                items: 3,
            },
            1200: {
                items: 5,
            },
            1400: {
                items: 6,
            },
        },
    });

    var tabPanes = document.querySelectorAll('.tab-pane');
    for (var i = 1; i < tabPanes.length; i++) {
        tabPanes[i].classList.remove('active', 'show');
    }
</script>
<link rel="stylesheet" href="/assets/vipanel_v2/dist/libs/owl.carousel/dist/assets/owl.carousel.min.css">