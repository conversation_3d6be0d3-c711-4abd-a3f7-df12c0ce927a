<?php
$sessionTitle = $recording_data->title;
$recordStartDate = $recording_data->start_date ?? '1970-01-01 00:00:00';
$recordEndDate = $recording_data->end_date ?? date('Y-m-d H:i:s');

$registerList = get_registers(site_config("project_id"));
if (is_array($registerList)) {
    if (!empty($sessionTitle)) {
        $registerList = array_filter($registerList, function ($item) use ($sessionTitle) {
            return $item->form_id == $sessionTitle;
        });
    }
    $registerList = array_map(function ($item) {
        return json_decode($item->data);
    }, $registerList);
} else {
    $registerList = [];
}

$registerEmails = array_column($registerList, 'email');
$registerCount = count($registerList);

$attendeeList = get_logins(site_config("project_id"));
$attendeeListGroped = array_reduce($attendeeList ?? [], function ($carry, $item) {
    $email = $item->email;
    $carry[$email][] = $item;
    return $carry;
}, array());

$attendeeListGroped = array_filter(
    $attendeeListGroped,
    function ($item) use ($registerEmails, $recordStartDate, $recordEndDate) {
        return in_array($item[0]->email, $registerEmails) &&
            $item[0]->create_date >= $recordStartDate &&
            $item[0]->create_date <= $recordEndDate;
    }
);

$attendeeCount = count($attendeeListGroped);
?>

<?php if (!empty($attendeeCount) || !empty($registerCount)) : ?>
    <div class="<?= $class ?>">
        <div class="card">
            <div class="card-body">
                <h5>Conversion Rate Overall</h5>
                <div id="chart-conversion-rate-overall"></div>
            </div>
        </div>
    </div>

    <script>
        var conversion_rate_overall_chart_options = {
            series: [<?= $attendeeCount ?>, <?= $registerCount - $attendeeCount ?>],
            chart: {
                fontFamily: '"Nunito Sans", sans-serif',
                height: 300,
                type: "donut",
            },
            colors: ["var(--bs-primary)", "var(--bs-danger)"],
            labels: ['Attendees', 'Registered Non-Participants'],
            legend: {
                show: false
            }
        };
        $(document).ready(function() {
            var conversion_rate_overall_chart = new ApexCharts(
                document.querySelector("#chart-conversion-rate-overall"),
                conversion_rate_overall_chart_options
            );
            conversion_rate_overall_chart.render();
        });
    </script>
<?php endif; ?>