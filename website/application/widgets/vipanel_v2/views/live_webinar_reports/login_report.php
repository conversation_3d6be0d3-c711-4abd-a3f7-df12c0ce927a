<?php
$this->load->helper('live_webinar_report');

$recordStartDate = $recording_data->start_date ?? '1970-01-01 00:00:00';
$recordEndDate = $recording_data->end_date ?? date('Y-m-d H:i:s');

$start_date = date('Y-m-d', strtotime($recordStartDate));
$end_date = date('Y-m-d', strtotime($recordEndDate));

$report = getLiveWebinarLoginReport($start_date, $end_date);

$columns = [
    "logID" => "logID",
    "Full Name" => "Full Name",
    "First Name" => "First Name",
    "Last Name" => "Last Name",
    "Email" => "Email",
    "Company" => "Company",
    "Country" => "Country",
    "Speciality" => "Speciality",
    "Afilliation" => "Afilliation",
    "Identity" => "Identity",
    "GSM" => "GSM",
    "Page" => "Page",
    "Number of Audiences" => "Number of Audiences",
    "Audiences" => "Audiences",
    "Browser" => "Browser",
    "Resolution" => "Resolution",
    "Internet speed" => "Internet speed",
    "Extra 1" => "Extra 1",
    "Extra 2" => "Extra 2",
    "Extra 3" => "Extra 3",
    "Extra 4" => "Extra 4",
    "Extra 5" => "Extra 5",
    "Extra 6" => "Extra 6",
    "Extra 7" => "Extra 7",
    "Extra 8" => "Extra 8",
    "Extra 9" => "Extra 9",
    "Extra 10" => "Extra 10",
    "Extra 11" => "Extra 11",
    "Extra 12" => "Extra 12",
    "Extra 13" => "Extra 13",
    "Extra 14" => "Extra 14",
    "Extra 15" => "Extra 15",
    "Total Duration (min.)" => "Total Duration (min.)",
    "IP" => "IP",
    "Check-in time" => "Check-in time",
    "Check-out time" => "Check-out time",
    "Register Time" => "Register Time",
    "Query String" => "Query String"
];
?>
<div class="table-responsive">
     <table class="table" id="login_report_result_table">
        <thead>
        <tr>
            <?php foreach ($columns as $key => $label): ?>
                <th scope="col"><?= $label ?></th>
            <?php endforeach; ?>
        </tr>
        </thead>
        <tbody>
        <?php if (!empty($report)): ?>
            <?php foreach ($report as $item): ?>
                <tr>
                    <?php foreach ($columns as $key => $label): ?>
                        <td><?= $item[$key] ?? '-' ?></td>
                    <?php endforeach; ?>
                </tr>
            <?php endforeach; ?>
        <?php endif; ?>
        </tbody>
    </table>
</div>

<script>
    const login_report_result_file = host + '_login_report_' + fileDateSuffix;
    datatableOption.scrollX = true;
    datatableOption.buttons = [
        {
            extend: 'copy',
            className: 'btn btn-warning btn-sm',
            title: login_report_result_file
        },
        {
            extend: 'csv',
            className: 'btn btn-warning btn-sm',
            title: login_report_result_file
        },
        {
            extend: 'excelHtml5',
            className: 'btn btn-warning btn-sm',
            title: login_report_result_file
        },
        {
            extend: 'pdfHtml5',
            className: 'btn btn-warning btn-sm',
            title: login_report_result_file
        },
        {
            extend: 'print',
            className: 'btn btn-warning btn-sm',
            title: login_report_result_file
        }
    ];
    datatableOption.autoWidth = false;
    datatableOption.scrollX = false;
    $('#login_report_result_table').DataTable(datatableOption);


    $(document).ready(function () {
        $('#login_report_result_table_wrapper .dt-buttons').addClass('btn-group');
        $('#login_report_result_table_wrapper').css({
            "overflow-x": "auto",
            "white-space": "nowrap"
        });
    });
</script>
