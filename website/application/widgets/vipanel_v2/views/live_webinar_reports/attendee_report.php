<?php
$pages = array_unique(array_column($data, 'page'));
$registration_mapping = json_decode(vipanel_project_config('registration_mapping'), true);
$site_timezone = project_config_default('site_timezone', 'Europe/Istanbul');
?>

<style>
    @media (min-width: 992px) {
        .lg-display-flex {
            display: inline-flex;
            float: right;
        }
    }

    @media (max-width: 576px) {
        .div-sm-block {
            display: block;
            width: 100%;
        }

        .dataTables_filter form label {
            margin-top: 15px;
        }
    }
</style>

<div class="card mt-2">
    <div class="card-header">
        <p class="me-3">
            Please ensure that you have accurately selected the appropriate webinar session from the upper right corner
            of
            this screen using the "<b>Session Records</b>" dropdown menu. Once you have chosen the correct session,
            please
            remember to click the "<b>List</b>" button.<br>
            <br>
            Additionally, you have the option to customize the information displayed in your final report by filtering
            through
            the "<b>Edit Columns</b>" section.
        </p>
    </div>
    <hr style="margin:0">
    <div class="card-header">
        <form class="d-md-flex justify-content-between flex-md-row flex-column">
            <div class="d-flex justify-content-between">
                <div class="d-flex align-items-center gap-2">
                    <div class="btn-group">
                        <button class="btn btn-danger btn-xs text-white" type="button" data-bs-toggle="modal"
                            data-bs-target="#modal-edit-columns">
                            Edit Columns
                        </button>
                        <button class="btn btn-info btn-xs text-white" type="button" data-bs-container="body"
                            data-bs-toggle="popover" data-bs-placement="bottom" data-bs-trigger="focus"
                            data-bs-content='You have the option to display or hide additional details of your attendees within your report through this section. Kindly specify the information to be included in your report before generating the report file.'>
                            <i class="fa fa-question-circle"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="text-right">
                <div class="gap-2 lg-display-flex">
                    <input type="hidden" name="recording_log" value="<?= $recording_data->id ?? null ?>">
                    <select class="form-control mt-2" name="tracked_page" style="">
                        <option value="">All Pages</option>
                        <?php foreach ($pages as $p): ?>
                            <option value="<?= $p; ?>" <?= $tracked_page == $p ? 'selected' : '' ?>>
                                <?= ucwords($p); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>

                    <button class="btn btn-primary btn-xs datatable_filter mt-2 d-sm-block div-sm-block" type="submit"
                        style="margin-left: 5px; border-radius: 4px;">List
                    </button>
                </div>
            </div>

            <?php foreach ($this->input->get('column', true) ?? [] as $key => $value): ?>
                <input type="hidden" name="column[<?= $key ?>]" value="<?= $value ?>">
            <?php endforeach; ?>
        </form>
    </div>
    <div class="card-body">
        <div class="table-responsive" style="overflow-x: auto;">
            <table id="live_webinar_reports_table" class="table" style="width: 100%">
                <thead>
                    <tr>
                        <th>E-mail</th>
                        <th>Page</th>
                        <th>Duration</th>
                        <th>Embed Language</th>
                        <?php foreach ($custom_columns as $column): ?>
                            <?php if (!empty($registration_mapping[$column])): ?>
                                <th>
                                    <?= ucwords($registration_mapping[$column]) ?>
                                </th>
                            <?php else: ?>
                                <th>
                                    <?= ucwords(implode(' ', explode('_', $column))) ?>
                                </th>
                            <?php endif; ?>
                        <?php endforeach; ?>
                        <th>Login Date Time</th>
                        <th>Last Action Date Time</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($data as $item): ?>
                        <tr>
                            <td>
                                <?= $item->email ?>
                            </td>
                            <td>
                                <span class="badge bg-info">
                                    <?= ucwords($item->page) ?>
                                </span>
                            </td>
                            <td>
                                <?= $item->duration ?>
                            </td>
                            <td>
                                <span class="badge bg-warning">
                                    <?= strtoupper($item->embed_lang ?? "-") ?>
                                </span>
                            </td>
                            <?php foreach ($custom_columns as $column): ?>
                                <th>
                                    <?= $item->{$column} ?? '-' ?>
                                </th>
                            <?php endforeach; ?>
                            <td data-order="<?= strtotime($register_list[$i]->create_date) ?>" style="font-family: monospace; font-size: 12px;">
                                <?php
                                $login_timestamp = new DateTime('now', new DateTimeZone($site_timezone));
                                $login_timestamp->setTimestamp($item->login_timestamp);
                                ?>
                                <?= $login_timestamp->format($site_date_format) ?>
                            </td>
                            <td data-order="<?= strtotime($register_list[$i]->create_date) ?>" style="font-family: monospace; font-size: 12px;">
                                <?php
                                $last_action_timestamp = new DateTime('now', new DateTimeZone($site_timezone));
                                $last_action_timestamp->setTimestamp($item->last_action_timestamp);
                                ?>
                                <?= $last_action_timestamp->format($site_date_format) ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php $this->load->view('live_webinar_reports/partials/edit_columns_modal'); ?>

<script>
    $(document).ready(function() {
        datatableOption.buttons = [{
                extend: 'copy',
                title: 'Copy',
                className: 'btn btn-primary btn-xs'
            },
            {
                extend: 'csv',
                title: function() {
                    return '<?= slugify($filtered_record['title'] ?? 'Live Webinar', '_') . '_report_' . date('Y-m-d_H:i:s') ?>'
                },
                className: 'btn btn-primary btn-xs'
            },
            {
                extend: 'excelHtml5',
                title: function() {
                    return '<?= slugify($filtered_record['title'] ?? 'Live Webinar', '_') . '_report_' . date('Y-m-d_H:i:s') ?>'
                },
                className: 'btn btn-primary btn-xs',
            },
            {
                extend: 'pdfHtml5',
                title: function() {
                    return '<?= slugify($filtered_record['title'] ?? 'Live Webinar', '_') . '_report_' . date('Y-m-d_H:i:s') ?>'
                },
                className: 'btn btn-primary btn-xs'
            },
            {
                extend: 'print',
                title: function() {
                    return '<?= slugify($filtered_record['title'] ?? 'Live Webinar', '_') . '_report_' . date('Y-m-d_H:i:s') ?>'
                },
                className: 'btn btn-primary btn-xs'
            }
        ]
        $('#live_webinar_reports_table').DataTable(datatableOption);
    });
</script>