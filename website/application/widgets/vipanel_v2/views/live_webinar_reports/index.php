<?php
$records = $this->security->xss_clean($records ?? []);

$permissions = $permissions['live_webinar_reports'] ?? [];
$site_timezone = project_config_default('site_timezone', 'Europe/Istanbul');

$recording_data = [];
$recording_id = $this->input->get('recording_log', true);
if (!empty($recording_id)) {
	$query = $this->db->from('records')
		->where('id', $recording_id)
		->where('project_id', site_config('project_id'))
		->get();
	if ($query) {
		$recording_data = $query->row();

		$recording_data->start_date = Date('Y-m-d H:i:s', $recording_data->start_timestamp);
		$recording_data->end_date = Date('Y-m-d H:i:s', $recording_data->ended_timestamp);
	}
}
?>
<div class="card">
	<?php
	if (
		$permissions['attendee_report'] ?? false ||
	$permissions['login_report'] ?? false ||
	$permissions['chat_report'] ?? false ||
	$permissions['polling_report'] ?? false ||
	$permissions['survey_report'] ?? false ||
	$permissions['webinar_metrics_insights'] ?? false
	) :
		?>
        <div class="card-header">
            <form>
                <div class="input-group mt-2" style="flex-wrap: nowrap; margin: 0 auto; max-width: 600px">
                    <button class="btn btn-info btn-xs text-white" type="button" data-bs-container="body"
                            data-bs-toggle="popover" data-bs-placement="bottom" data-bs-trigger="focus"
                            data-bs-content='To access the accurate report, kindly choose your webinar
                        session based on the session date and time, followed by clicking the "List" button.
                        The reports within this section will then be presented according to your specified selection.'>
                        <i class="fa fa-question-circle"></i>
                    </button>
                    <select class="form-select" name="recording_log" style="width: 300px;">
                        <option value="">Session Records</option>
						<?php foreach ($records as $key => $record) : ?>
                            <option value="<?= $record['id']; ?>"
								<?= $record['id'] == $recording_log ? 'selected' : '' ?>>
								<?=
								($record['title'] ?? 'Untitle') . ' | ' .
								date('H:i', strtotime($record['start_timestamp_date'])) .
								' - ' .
								date('H:i', strtotime($record['ended_timestamp_date'])) .
								' / ' .
								date('d M Y', strtotime($record['start_timestamp_date']));
								?>
                            </option>
						<?php endforeach; ?>
                    </select>
                    <button class="btn btn-danger btn-xs text-white" type="button" data-bs-toggle="modal"
                            data-bs-target="#modal-edit-sessions">
                        Edit Session Name
                    </button>
                    <button class="btn btn-info btn-xs text-white" type="submit">
                        List
                    </button>
                </div>
            </form>
        </div>
	<?php endif; ?>
    <div class="card-body">
        <ul class="nav nav-pills flex-column flex-sm-row mb-3" role="tablist" id="v-pills-tab">
			<?php if ($permissions['attendee_report'] ?? false) : ?>
                <li class="nav-item flex-sm-fill mb-2 text-sm-center">
                    <a class="nav-link fw-semibold" id="v-attendee-report-tab" data-bs-toggle="pill"
                       href="#v-attendee-report" role="tab" aria-controls="v-attendee-report" aria-selected="true">
                        Attendee Report
                    </a>
                </li>
			<?php endif; ?>
            <?php if ($permissions['login_report'] ?? false) : ?>
                <li class="nav-item flex-sm-fill mb-2 text-sm-center">
                    <a class="nav-link fw-semibold" id="v-login-report-tab" data-bs-toggle="pill"
                       href="#v-login-report" role="tab" aria-controls="v-login-report" aria-selected="true">
                        Login Report
                    </a>
                </li>
            <?php endif; ?>
			<?php if ($permissions['chat_report'] ?? false) : ?>
                <li class="nav-item flex-sm-fill mb-2 text-sm-center">
                    <a class="nav-link fw-semibold" id="v-chat-report-tab" data-bs-toggle="pill"
                       href="#v-chat-report" role="tab" aria-controls="v-chat-report" aria-selected="true">
                        Chat Report
                    </a>
                </li>
			<?php endif; ?>
			<?php if ($permissions['polling_report'] ?? false) : ?>
                <li class="nav-item flex-sm-fill mb-2 text-sm-center">
                    <a class="nav-link fw-semibold" id="v-polling-report-tab" data-bs-toggle="pill"
                       href="#v-polling-report" role="tab" aria-controls="v-polling-report" aria-selected="true">
                        Polling Report
                    </a>
                </li>
			<?php endif; ?>
			<?php if ($permissions['survey_report'] ?? false) : ?>
                <li class="nav-item flex-sm-fill mb-2 text-sm-center">
                    <a class="nav-link fw-semibold" id="v-survey-report-tab" data-bs-toggle="pill"
                       href="#v-survey-report" role="tab" aria-controls="v-survey-report" aria-selected="true">
                        Survey Report
                    </a>
                </li>
			<?php endif; ?>
			<?php if ($permissions['reaction_report'] ?? false) : ?>
                <li class="nav-item flex-sm-fill mb-2 text-sm-center">
                    <a class="nav-link" id="v-reaction-report-tab" data-bs-toggle="pill"
                       href="#v-reaction-report" role="tab" aria-controls="v-reaction-report"
                       aria-selected="false">
                        Reaction Report
                    </a>
                </li>
			<?php endif; ?>
			<?php if ($permissions['webinar_metrics_insights'] ?? false) : ?>
                <li class="nav-item flex-sm-fill mb-2 text-sm-center">
                    <a class="nav-link" id="v-webinar-metrics-insights-tab" data-bs-toggle="pill"
                       href="#v-webinar-metrics-insights" role="tab" aria-controls="v-webinar-metrics-insights"
                       aria-selected="false">
                        Webinar Metrics and Insights
                    </a>
                </li>
			<?php endif; ?>
        </ul>

        <div class="tab-content" id="v-pills-tabContent">
			<?php if ($permissions['attendee_report'] ?? false) : ?>
                <div class="tab-pane fade" id="v-attendee-report" role="tabpanel"
                     aria-labelledby="v-attendee-report-tab">
					<?php
					$this->load->view(
						'live_webinar_reports/attendee_report',
						['recording_data' => $recording_data]
					);
					?>
                </div>
			<?php endif; ?>
			<?php if ($permissions['login_report'] ?? false) : ?>
                <div class="tab-pane fade" id="v-login-report" role="tabpanel" aria-labelledby="v-login-report-tab">
					<?php
					$this->load->view(
						'live_webinar_reports/login_report',
						['recording_data' => $recording_data]
					);
					?>
                </div>
			<?php endif; ?>
            <?php if ($permissions['chat_report'] ?? false) : ?>
                <div class="tab-pane fade" id="v-chat-report" role="tabpanel" aria-labelledby="v-chat-report-tab">
                    <?php
                    $this->load->view(
                        'live_webinar_reports/chat_report',
                        ['recording_data' => $recording_data]
                    );
                    ?>
                </div>
            <?php endif; ?>
			<?php if ($permissions['polling_report'] ?? false) : ?>
                <div class="tab-pane fade" id="v-polling-report" role="tabpanel" aria-labelledby="v-polling-report-tab">
					<?php
					$this->load->view(
						'live_webinar_reports/polling_report',
						['recording_data' => $recording_data]
					);
					?>
                </div>
			<?php endif; ?>
			<?php if ($permissions['survey_report'] ?? false) : ?>
                <div class="tab-pane fade" id="v-survey-report" role="tabpanel" aria-labelledby="v-survey-report-tab">
					<?php
					$this->load->view(
						'live_webinar_reports/survey_report',
						['recording_data' => $recording_data]
					);
					?>
                </div>
			<?php endif; ?>
			<?php if ($permissions['reaction_report'] ?? false) : ?>
                <div class="tab-pane fade" id="v-reaction-report" role="tabpanel"
                     aria-labelledby="v-reaction-report-tab">
					<?php $this->load->view('live_webinar_reports/reaction_report'); ?>
                </div>
			<?php endif; ?>
			<?php if ($permissions['webinar_metrics_insights'] ?? false) : ?>
                <div class="tab-pane fade" id="v-webinar-metrics-insights" role="tabpanel"
                     aria-labelledby="v-webinar-metrics-insights-tab">
					<?php $this->load->view('live_webinar_reports/webinar_metrics_insights'); ?>
                </div>
			<?php endif; ?>
        </div>
    </div>
</div>

<?php
if (
	$permissions['attendee_report'] ?? false ||
        $permissions['login_report'] ?? false ||
        $permissions['chat_report'] ?? false ||
        $permissions['polling_report'] ?? false ||
        $permissions['survey_report'] ?? false ||
        $permissions['webinar_metrics_insights'] ?? false
    ) {
        $this->load->view('live_webinar_reports/partials/edit_sessions_modal', ['sessions' => $records]);
    }
?>

<script>
    const openType = '<?= $this->input->get('type', true); ?>';
    const permissions = <?= json_encode($permissions) ?>;
    $(document).ready(function () {
        if (permissions[openType]) {
            var targetElement = null;
            switch (openType) {
                case "attendee_report":
                    targetElement = 'v-attendee-report';
                    break;
                case "login_report":
                    targetElement = 'v-login-report';
                    break;
                case "chat_report":
                    targetElement = 'v-chat-report';
                    break;
                case "polling_report":
                    targetElement = 'v-polling-report';
                    break;
                case "survey_report":
                    targetElement = 'v-survey-report';
                    break;
                case "reaction_report":
                    targetElement = 'v-reaction-report';
                    break;
                case "webinar_metrics_insights":
                    targetElement = 'v-webinar-metrics-insights';
                    break;
                default:
                    break;
            }
            if (targetElement !== null) {
                $('#v-pills-tabContent .tab-pane:not(.disabled)#' + targetElement).addClass('show active');
                $('#v-pills-tab .nav-link:not(.disabled)[href="#' + targetElement + '"]').addClass('active');
            } else {
                $('#v-pills-tabContent .tab-pane:not(.disabled)').first().addClass('show active');
                $('#v-pills-tab .nav-link:not(.disabled)').first().addClass('active');
            }
        } else {
            $('#v-pills-tabContent .tab-pane:not(.disabled)').first().addClass('show active');
            $('#v-pills-tab .nav-link:not(.disabled)').first().addClass('active');
        }
    });
</script>
<script src="/assets/vipanel_v2/dist/libs/apexcharts/dist/apexcharts.min.js"></script>