<?php
$site_timezone = project_config_default('site_timezone', 'Europe/Istanbul');
$title = ucwords(str_replace('_', ' ', $type ?? ""));
$get_input = $this->input->get(null, true);
$search_array = ["column"];
foreach ($get_input as $key => $value) {
    if ($value === $search_array) {
        unset($get_input[$key]);
        break;
    }
}
$last_session = end($records_data);

$session_closed = 1;
if (!empty($last_session)) {
    $session_closed = $last_session['ended_timestamp'] !== null;
}

$streamCodes = getStreamCodes();
$streamCodes[] = 'Test Session';
foreach ($streamCodes as $key => $value) {
    unset($streamCodes[$key]);
    $streamCodes[$value] = $value;
}

?>
<div class="card">
    <div class="card-body">
        <div class="d-flex justify-content-between mb-3">
            <div class="d-flex gap-2 align-items-center">
                <button class="btn btn-danger btn-sm text-white"
                    onclick="$('#modal-edit-tracked-time-columns').modal('show')">Edit Columns
                </button>
                <button class="btn btn-warning btn-sm text-white"
                    onclick="$('#modal-edit-session-columns').modal('show')"><i class="fa fa-edit"></i> Edit Session
                    Name
                </button>

                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="toggleDetailedColumns" 
                            onchange="window.location.href = '/vipanel/v2/admin-settings/reports/tracked_time_report?detailed_columns=' + this.checked"
                            <?= $options['detailed_columns'] ? 'checked' : '' ?>>
                    <label class="form-check-label" for="toggleDetailedColumns">Show Detailed Durations</label>
                </div>
            </div>
            <div class="recording-buttons d-flex gap-2 align-items-center justify-content-center" id="recording-buttons">
                <div id="recording-status">
                    Record Status: <?= $session_closed ? 'Stopped' : 'Recording' ?>
                </div>
                <?php if ($session_closed) : ?>
                    <button class="btn btn-success btn-sm" id="start-btn" data-name="recording_webinar"
                        data-id="start-btn" data-value="1" onclick="updateConfigAdmin(this)" style="color:white;">
                        Start Record
                    </button>
                    <button class="btn btn-danger btn-sm disabled" id="stop-btn" data-name="recording_webinar"
                        data-id="stop-btn" data-value="0" onclick="updateConfigAdmin(this)" style="color:white;">
                        Stop Record
                    </button>
                <?php else : ?>
                    <button class="btn btn-success btn-sm disabled" id="start-btn" data-name="recording_webinar"
                        data-id="start-btn" data-value="1" onclick="updateConfigAdmin(this)" style="color:white;">
                        Start Record
                    </button>
                    <button class="btn btn-danger btn-sm" id="stop-btn" data-name="recording_webinar" data-id="stop-btn"
                        data-value="0" onclick="updateConfigAdmin(this)" style="color:white;">Stop Record
                    </button>
                <?php endif; ?>
            </div>
        </div>
        <?php $this->load->view('admin_settings/partials/report_filter', ['position' => 'center']); ?>
        <?php $this->load->view('admin_settings/partials/edit_tracked_time_columns', ['columnListByRegister' => $columnListByRegister, 'get_input' => $get_input]); ?>
        <?php $this->load->view('admin_settings/partials/edit_session_columns', ['records_data' => $records_data, 'streamCodes' => $streamCodes]); ?>

        <div class="table-responsive">
            <table id="tracked_time_report" class="table border table-striped table-bordered display text-nowrap">
                <thead>
                    <tr>
                        <?php foreach ($columns as $column) : ?>
                            <th><?= htmlspecialchars($column) ?></th>
                        <?php endforeach; ?>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($data as $row) : ?>
                        <tr>
                            <?php foreach ($columns as $key => $column) : ?>
                                <?php if (isStringDate($row->$key)) : ?>
                                    <td data-order="<?= strtotime($row->$key) ?>" class="date-row">
                                        <?= $row->$key ?>
                                    </td>
                                <?php elseif (strpos($key, 'embed_durations.') !== false) : ?>
                                    <?php $key = str_replace('embed_durations.', '', $key); ?>
                                    <td data-order="<?= $row->embed_durations[$key] ?>">
                                        <?= isset($row->embed_durations[$key]) ? strtoupper(htmlspecialchars($row->embed_durations[$key])) : '-' ?>
                                    </td>
                                <?php else : ?>
                                    <td>
                                        <?= isset($row->$key) ? htmlspecialchars($row->$key) : '-' ?>
                                    </td>
                                <?php endif; ?>
                            <?php endforeach; ?>
                            <td>
                                <a class="btn btn-primary btn-sm"
                                    href="/vipanel/v2/admin-settings/reports/tracked-time/detail?email=<?= $row->email ?>&page=<?= $row->page ?>">Details</a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
    const type = "<?= htmlspecialchars($type, ENT_QUOTES, 'UTF-8') ?>";
    const reportTitle = host + '_' + type + '_report_' + fileDateSuffix;

    datatableOption.autoWidth = false;
    datatableOption.scrollX = true;
    datatableOption.buttons = [{
        extend: 'copyHtml5',
        className: 'btn btn-warning btn-sm',
        title: reportTitle
    }, {
        extend: 'csvHtml5',
        className: 'btn btn-warning btn-sm',
        title: reportTitle
    }, {
        extend: 'excelHtml5',
        className: 'btn btn-warning btn-sm',
        title: reportTitle
    }, {
        extend: 'pdfHtml5',
        className: 'btn btn-warning btn-sm',
        title: reportTitle
    }, {
        extend: 'print',
        className: 'btn btn-warning btn-sm',
        title: reportTitle
    }];
    datatableOption.initComplete = function() {
        $(this.api().table().container())
            .find('input[type="search"]')
            .parent()
            .wrap('<form>')
            .parent()
            .attr('autocomplete', 'off')
            .css('overflow', 'hidden')
            .css('margin', 'auto');

        $('.dt-buttons').addClass('btn-group');
        $('.dt-buttons .dt-button').addClass('btn btn-sm btn-warning');
    }

    datatableOption.columnDefs = [{
        "orderable": false,
        "targets": 5
    }]
    var dt = $('#tracked_time_report').DataTable(datatableOption);

    function updateConfigAdmin(e) {
        var button_id = $(e).data('id');
        var name = $(e).data('name');
        var value = $(e).data('value');
        if (value === 0) {
            Swal.fire({
                title: "Please select the session title.",
                input: "select",
                inputOptions: <?= json_encode($streamCodes) ?>,
                inputPlaceholder: "Select a session code as title",
                showCancelButton: true,
                inputValidator: (value) => {
                    return new Promise((resolve) => {
                        if (value !== '') {
                            resolve();
                        } else {
                            resolve("You need to select a session title!");
                        }
                    });
                }
            }).then((result) => {
                if (result.value) {
                    updateRecordingTimeAdmin(button_id, value, result.value);
                    toastr.success('Recording stopped!');
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                }
            });
        } else {
            updateRecordingTimeAdmin(button_id, value);
            toastr.success('Recording started!');
        }
    }

    function updateRecordingTimeAdmin(button_id, value, title) {
        $.ajax({
            url: '/vipanel/v2/admin-settings/recording_time_logger',
            type: 'POST',
            data: {
                value: value,
                title: title
            },
            success: function(response) {
                var button = $('#' + button_id);
                if (value === 1) {
                    $(button).prop('disabled', true).addClass('disabled');
                    $('#stop-btn').prop('disabled', false).removeClass('disabled');
                    $('#recording-status').text('Record Status: Recording');
                } else {
                    $(button).prop('disabled', true).addClass('disabled');
                    $('#start-btn').prop('disabled', false).removeClass('disabled');
                    $('#recording-status').text('Record Status: Stopped');
                }
            },
        })
    }

    function updateSessionTitleAdmin(id, title) {
        $.post(
            '/ajax/post/recording_time/update-title', {
                id: id,
                title: title
            },
            function(response) {
                response = JSON.parse(response);
                if (response.status) {
                    alert('Updated!');
                } else {
                    alert('Something went wrong!');
                }
            },
        );
    }
</script>