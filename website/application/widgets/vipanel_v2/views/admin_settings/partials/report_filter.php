<?php
$options = $this->security->xss_clean($options ?? []);

switch ($position ?? null) {
    case "center":
        $positionClass = "justify-content-center";
        break;
    case "right":
        $positionClass = "justify-content-end";
        break;
    default:
        $positionClass = "justify-content-start";
        break;
}
?>
<?php if ($options['is_option']) : ?>
    <form action="/vipanel/v2/admin-settings/reports/<?= $this->uri->segment(5) ?>" method="get">
        <div class="d-flex gap-3 <?= $positionClass ?>">
            <?php if ($options['time_filter']) : ?>
                <div class="d-flex gap-2 align-items-center">
                    <label for="date1" style="text-wrap: nowrap">Start Date :</label>
                    <input type="date" name="start_date" class="form-control form-control-sm"
                        id="date1" value="<?= $options['date']['start_date'] ?>">
                </div>
                <div class="d-flex gap-2 align-items-center">
                    <label for="date2" style="text-wrap: nowrap">End Date :</label>
                    <input type="date" name="end_date" class="form-control form-control-sm"
                        id="date2" value="<?= $options['date']['end_date'] ?>">
                </div>

            <?php endif; ?>

            <?php if ($options['select_filter'] && !empty($options['select_filter'])) : ?>
                <div class="d-flex gap-2 align-items-center">
                    <select class="form-select form-select-sm"
                        id="<?= $options['select_filter']['select_filter_name'] ?>"
                        name="<?= $options['select_filter']['select_filter_name'] ?>">
                        <option value=""><?= $options['select_filter']['select_filter_title'] ?? 'Select an option' ?></option>
                        <?php
                        unset(
                            $options['select_filter']['select_filter_name'],
                            $options['select_filter']['select_filter_title']
                        );
                        foreach ($options['select_filter'] as $key => $value) :
                        ?>
                            <option
                                value="<?= $key ?>" <?= $key == $options['select_choose'] ? 'selected' : '' ?>>
                                <?= $value ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            <?php endif; ?>

            <?php if ($options['extra_select_filter'] && !empty($options['extra_select_filter'])) : ?>
                <div class="d-flex gap-2 align-items-center">
                    <select class="form-select form-select-sm"
                        id="<?= $options['extra_select_filter']['extra_select_filter_name'] ?>"
                        name="<?= $options['extra_select_filter']['extra_select_filter_name'] ?>">
                        <option value=""><?= $options['extra_select_filter']['extra_select_filter_title'] ?? 'Select an option' ?></option>
                        <?php
                        unset(
                            $options['extra_select_filter']['extra_select_filter_name'],
                            $options['extra_select_filter']['extra_select_filter_title']
                        );
                        foreach ($options['extra_select_filter'] as $key => $value) :
                        ?>
                            <option
                                value="<?= $key ?>" <?= $key == $options['extra_select_choose'] ? 'selected' : '' ?>>
                                <?= $value ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            <?php endif; ?>

            <?php if ($options['hidden_domains'] ?? false) : ?>
                <div class="d-flex gap-2 align-items-center">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="hidden_domains" name="hidden_domains" value="1" <?= isset($_GET['hidden_domains']) ? 'checked' : '' ?>>
                        <label class="form-check-label" for="hidden_domains">Show Hidden Domains</label>
                    </div>
                </div>
            <?php endif; ?>

            <div>
                <input type="submit" class="btn waves-effect waves-light btn-primary btn-sm create-user-btn"
                    value="List" class="form-control" id="date1">
            </div>
        </div>
        <input type="hidden" name="report_type" value="<?= $type ?>">
    </form>
    <hr>
<?php endif; ?>