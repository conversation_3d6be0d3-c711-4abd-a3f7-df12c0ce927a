<div class="modal fade" id="modal-edit-session-columns" tabindex="-1" aria-labelledby="modal-edit-session-columns"
     aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
            <form>
                <div class="modal-header">
                    <h4 class="modal-title" id="myModalLabel">Edit Sessions</h4>
                    <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"
                            onclick="$('#modal-edit-session-columns').modal('hide')"></button>
                </div>
                <div class="modal-body">
                    <?php foreach ($records_data as $key => $data) : ?>
                        <div class="input-group mb-3">
                            <input type="text" class="form-control" value="<?= $data['just_date'] ?>" disabled>
                            <select class="form-control">
                                <?php foreach ($streamCodes as $code): ?>
                                    <option value="<?= $code ?>" <?= $code == $data['just_title'] ? 'selected' : ''?>>
                                        <?= htmlspecialchars($code) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <button type="button" class="btn btn-sm btn-warning"
                                    onclick="updateSessionTitleAdmin('<?= $data['id']; ?>', $(this).prev().val())">
                                <i class="fa fa-save"></i>
                            </button>
                        </div>
                    <?php endforeach; ?>
                </div>
            </form>
        </div>
    </div>
</div>

