<?php
$data = json_decode(json_encode($data ?? []), true);
$columns = $this->security->xss_clean($columns ?? []);
$data = $this->security->xss_clean($data);
$data = json_decode(json_encode($data), false);
?>
<style>
    #loading-indicator {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background-color: rgba(255, 255, 255, 0.8);
        padding: 15px 25px;
        border-radius: 5px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        z-index: 1001;
        display: none;
        font-weight: 600;
        color: #333;
        border: 1px solid #ddd;
    }

    #loading-indicator:before {
        content: '';
        display: inline-block;
        width: 20px;
        height: 20px;
        margin-right: 10px;
        border: 3px solid #3498db;
        border-radius: 50%;
        border-top-color: transparent;
        animation: spin 1s linear infinite;
        vertical-align: middle;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }
</style>

<?php if ($alert = $this->session->flashdata('import_alert')): ?>
    <div class="alert alert-<?= $alert['success'] ? 'success' : 'danger' ?>">
        <?= $alert['message'] ?>
    </div>
    <?php $this->session->unset_userdata('import_alert'); ?>
<?php endif; ?>

<div class="card">
    <div class="card-body p-4">
        <div class="mt-2 mb-4">
            <form class="row" action="/vipanel/v2/admin-settings/afme-users-import" enctype="multipart/form-data" method="post" accept-charset="utf-8">
                <input type="hidden" name="<?= $this->security->get_csrf_token_name(); ?>" value="<?= $this->security->get_csrf_hash(); ?>" />

                <div class="col-8 col-lg-4">
                    <div class="form-group">
                        <input type="file" name="importFile" class="form-control" placeholder="Import File">
                    </div>
                </div>
                <div class="col-4 col-lg-3">
                    <button class="btn btn-light" type="submit" style="margin-right: 10px;"><i
                                class="fa fa-upload"></i> Import
                    </button>
                </div>
            </form>
        </div>
        <hr>
        <div class="table-responsive rounded-2 mb-4">
            
            <div id="loading-indicator" style="">Loading...</div>

            <table id="afme-users-table" class="table border text-nowrap customize-table mb-0 align-middle">
                <thead class="text-dark fs-4">
                <tr>
                    <?php foreach ($columns as $column) : ?>
                        <th><h6 class="fs-4 fw-semibold mb-0"><?= $column ?></h6></th>
                    <?php endforeach; ?>
                </tr>
                </thead>
                <tbody>

                </tbody>
            </table>
        </div>
    </div>
</div>

<script type="text/javascript" charset="utf-8">
    $(document).ready(function () {
        $('#loading-indicator').show();

        var table = $('#afme-users-table').DataTable({
            dom: 'Bfrtip',
            buttons: [ ],
            'processing': true,
            'serverSide': true,
            'serverMethod': 'post',
            'ajax': {
                'url': '/vipanel/v2/admin-settings/afme-users-ajax',
                'data': function(d) {
                    var value = "; " + document.cookie;
                    var parts = value.split("; csrf_vs_cookie=");
                    if (parts.length == 2)
                        d.csrf_vs_token = parts.pop().split(";").shift();
                    return d;
                }
            },
            'columns': [
                {data: 'email'},
                {data: 'country'},
            ]
        });

        table.on('processing.dt', function(e, settings, processing) {
            if (processing) {
                $('#loading-indicator').show();
                $('#table-overlay').show();
            } else {
                $('#loading-indicator').hide();
                $('#table-overlay').hide();
            }
        });

        // İlk ajax tamamlandığında loading'i gizle
        table.on('xhr.dt', function () {
            $('#loading-indicator').hide();
        });
    });
</script>