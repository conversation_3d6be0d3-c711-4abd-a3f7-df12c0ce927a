<?php
$register_list = get_registers(site_config('project_id')) ?? [];
$approved_users = vipanel_approval_users(site_config('project_id'));
$declined_users = vipanel_approval_users(site_config('project_id'), false);

$register_list = array_map(function ($item) use ($approved_users, $declined_users) {
    $item_data = json_decode($item->data);
    $search = in_array($item_data->email, array_column($approved_users, 'email'));
    if ($search !== false) {
        $item_data->confirmation = true;
    }
    $search = in_array($item_data->email, array_column($declined_users, 'email'));
    if ($search !== false) {
        $item_data->confirmation = false;
    }
    $item->data = json_encode($item_data);
    return $item;
}, (array) $register_list);

$hiddenDomains = json_decode(vipanel_project_config('hidden_domains'));
$register_list = array_filter($register_list, function ($value) use ($hiddenDomains) {
    $data = json_decode($value->data);
    $email = explode('@', $data->email);
    $domain = $email[1] ?? null;
    return !in_array($domain, $hiddenDomains ?? []);
});

$register_list = array_values($register_list);

$register_columns = get_register_columns(site_config('project_id'));
if (empty($register_columns)) {
    $register_columns = [];
}

$registration_mapping = json_decode(vipanel_project_config('registration_mapping'), true);

if (!empty($registration_mapping)) {
    if (!array_key_exists('confirmation', $registration_mapping)) {
        $registration_mapping['confirmation'] = 'Confirmation';
    }

    foreach ($register_list as $key => $item) {
        $editedData = [];
        foreach (json_decode($item->data, true) as $k => $v) {
            if (in_array($k, array_keys($registration_mapping))) {
                $editedData[$k] = $v;
            }
        }
        $item->data = json_encode($editedData);
        $register_list[$key] = $item;
    }
    $editedRegisterColumns = [];
    foreach ($register_columns as $k => $v) {
        if (in_array($k, array_keys($registration_mapping))) {
            $editedRegisterColumns[$k] = $v;
        }
    }
    $register_columns = $editedRegisterColumns;
}

$form_list = array_map(function ($item) {
    return $item->form_name;
}, $register_list);
$form_list = array_values(array_unique($form_list));

$selectedFormName = $this->input->get('form_name', true) ?? null;
if (!empty($selectedFormName)) {
    $register_list = array_filter($register_list, function ($item) use ($selectedFormName) {
        return $item->form_name == $selectedFormName;
    });
}

$register_list = array_values($register_list);
?>
<div>
    <div class="d-flex justify-content-between">
        <ul class="nav nav-pills" role="tablist">
            <li class="nav-item custom-w-100-sm">
                <a class="nav-link active mb-2" data-bs-toggle="tab" href="#vipanel-awaiting-approval" role="tab">
                    <span>Awaiting Approval</span>
                </a>
            </li>
            <li class="nav-item custom-w-100-sm">
                <a class="nav-link mb-2" data-bs-toggle="tab" href="#vipanel-approved" role="tab">
                    <span>Approved</span>
                </a>
            </li>
            <li class="nav-item custom-w-100-sm">
                <a class="nav-link mb-2" data-bs-toggle="tab" href="#vipanel-declined" role="tab">
                    <span>Declined</span>
                </a>
            </li>
        </ul>
        <button class="btn btn-success mb-2" id="show-confirmation-list" data-bs-toggle="modal"
                data-bs-target="#modal-approval-list" style="margin-left: 10px;">
            Lists
        </button>
    </div>
    <hr />
    <div class="tab-content mt-2">
        <div class="tab-pane active py-3" id="vipanel-awaiting-approval" role="tabpanel">
            <?php $this->load->view('registration_management/partials/awaiting_list',
                [
                        'registration_mapping' => $registration_mapping,
                        'columns' => $register_columns,
                        'register_list' => $register_list
                ]);
            ?>
        </div>
        <div class="tab-pane py-3" id="vipanel-approved" role="tabpanel">
            <?php $this->load->view('registration_management/partials/approve_list',
                [
                        'registration_mapping' => $registration_mapping,
                        'columns' => $register_columns,
                        'register_list' => $register_list
                ]); ?>
        </div>
        <div class="tab-pane py-3" id="vipanel-declined" role="tabpanel">
            <?php $this->load->view('registration_management/partials/decline_list',
                [
                        'registration_mapping' => $registration_mapping,
                        'columns' => $register_columns,
                        'register_list' => $register_list
                ]); ?>
        </div>
    </div>
    <?php if (empty(project_config('vipanel_access'))) : ?>
    <div class="alert customize-alert alert-dismissible alert-light-warning text-warning fade show p-0" role="alert">
        <div class="font-medium me-md-0">
            <i class="ti ti-info-circle fs-5 me-2 text-warning"></i>
            Please turn on "<b>vipanel_access</b>" setting for access control based on users' approval or rejection status.
        </div>
    </div>
    <?php endif; ?>
</div>

<?php $this->load->view('registration_management/partials/approval_modal'); ?>

<script>
    $(document).ready(function () {
        registerManagement.init()
    });

    let registerManagement = {
        data: {
            totalCount: 0,
            approveList: [],
            declineList: [],
            map: []
        },
        init: function () {
            this.update();
        },
        update: function () {
            $('#show-confirmation-list').html('Lists (' + this.data.totalCount + ')');
            if (this.data.totalCount < 1) {
                $('#show-confirmation-list').addClass('disabled');
                $('#modal-approval-list').modal('hide');
            } else {
                $('#show-confirmation-list').removeClass('disabled');
            }

            const tbody = document.querySelector('#modal-approval-list table tbody');
            tbody.innerHTML = '';
            this.data.approveList.forEach(item => {
                const tableRow = document.createElement('tr');

                const cellStatus = document.createElement('td');
                cellStatus.innerHTML = '<span class="badge bg-success">Approve</span>';
                tableRow.appendChild(cellStatus);

                const cellEmail = document.createElement('td');
                cellEmail.textContent = item;
                tableRow.appendChild(cellEmail);

                const cellAction = document.createElement('td');
                cellAction.innerHTML = '<button class="btn btn-danger btn-sm" onclick="registerManagement.remove(\'' + item + '\')">Cancel</button>';
                tableRow.appendChild(cellAction);

                tbody.appendChild(tableRow);
            });
            this.data.declineList.forEach(item => {
                const tableRow = document.createElement('tr');

                const cellStatus = document.createElement('td');
                cellStatus.innerHTML = '<span class="badge bg-danger">Decline</span>';
                tableRow.appendChild(cellStatus);

                const cellEmail = document.createElement('td');
                cellEmail.textContent = item;
                tableRow.appendChild(cellEmail);

                const cellAction = document.createElement('td');
                cellAction.innerHTML = '<button class="btn btn-danger btn-sm" onclick="registerManagement.remove(\'' + item + '\')">Cancel</button>';
                tableRow.appendChild(cellAction);

                tbody.appendChild(tableRow);
            });
        },
        add: function (email, type, row, item) {
            if (type === 'approve') {
                this.data.approveList.push(email);
                this.data.approveList = this.data.approveList.filter(onlyUnique);
                if (this.data.declineList.indexOf(email) !== -1) {
                    this.data.declineList.splice(this.data.declineList.indexOf(email), 1);
                } else {
                    this.data.totalCount++;
                }
            } else if (type === 'decline') {
                this.data.declineList.push(email);
                this.data.declineList = this.data.declineList.filter(onlyUnique);
                if (this.data.approveList.indexOf(email) !== -1) {
                    this.data.approveList.splice(this.data.approveList.indexOf(email), 1);
                } else {
                    this.data.totalCount++;
                }
            }
            this.data.map.push({
                row: row,
                email: email
            });
            $('#' + row).find('.disabled').removeClass('disabled');
            $(item).addClass('disabled');
            this.update();
        },
        remove: function (email) {
            const search = this.data.map.find(item => item.email === email);
            $('#' + search.row).find('.disabled').removeClass('disabled');

            if (this.data.approveList.indexOf(email) !== -1) {
                this.data.approveList.splice(this.data.approveList.indexOf(email), 1);
            }
            if (this.data.declineList.indexOf(email) !== -1) {
                this.data.declineList.splice(this.data.declineList.indexOf(email), 1);
            }
            this.data.totalCount--;
            this.update();
        },
        save: function () {
            $('.preloader').fadeIn();
            $.ajax({
                url: '/vipanel/v2/registration-management/save',
                type: 'POST',
                data: {
                    type: 'vipanelApprove',
                    approveList: this.data.approveList,
                    declineList: this.data.declineList,
                },
                success: function (result) {
                    if (result.status) {
                        reloadWithParam('type', 'register_management')
                    }
                },
                error: function () {
                    $('.preloader').fadeOut();
                }
            });
        }
    }
</script>
