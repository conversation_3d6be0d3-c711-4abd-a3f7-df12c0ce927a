<div class="table-responsive mt-3" style="overflow-x: auto;">
    <table class="table text-nowrap table-bordered" id="vipanelApproveTable">
        <thead>
        <tr>
			<?php foreach ($columns as $key => $column) : ?>
                <th><?= $registration_mapping[$key] ?? $column ?></th>
			<?php endforeach; ?>
            <th>Form Name</th>
            <th>Form ID</th>
            <th>Mailing</th>
            <th>Verify</th>
        </tr>
        </thead>
        <tbody>
		<?php
		foreach ($register_list as $j => $item) :
            $data = json_decode($item->data ?? "", true);
            unset($data['form_name'], $data['form_id'], $data['create_date']);

            if (isset($data['confirmation']) && $data['confirmation']):
        ?>
                <tr id="approve-row-<?= $j ?>">
					<?php foreach ($columns as $key => $column): ?>
                        <td><?= $data[$key] ?? '' ?></td>
					<?php endforeach; ?>
                    <td>
                        <?= $item->form_name ?>
                    </td>
                    <td>
                        <?php if (!empty($item->form_id)): ?>
                            <a class="btn btn-sm btn-outline-primary btn-sm" onclick="streamDetail.load('<?= $register_list[$i]->form_id ?>')">
                                <?= $item->form_id ?>
                            </a>
                        <?php else: ?>
                            -
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php if ($data['confirmation_mail'] ?? false) : ?>
                            <span class="badge badge-success"><i class="fa fa-check"></i></span>
                        <?php else : ?>
                            <span class="badge badge-danger"><i class="fa fa-close"></i></span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <a class="btn btn-danger btn-accept btn-sm"
                           onclick="registerManagement.add('<?= $data['email'] ?>', 'decline', 'approve-row-<?= $j ?>', this)">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-x"
                                 width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor"
                                 fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                <path d="M18 6l-12 12"/>
                                <path d="M6 6l12 12"/>
                            </svg>
                        </a>
                    </td>
                </tr>
			<?php endif; ?>
		<?php endforeach; ?>
        </tbody>
    </table>
</div>

<script defer>
    const approve_file = host + '_approved_' + fileDateSuffix;
    registerManagementDatatableOptions.buttons = [
        {
            extend: 'copyHtml5',
            title: approve_file,
            exportOptions: {
                columns: <?= json_encode(array_keys(array_values($columns))) ?>,
                format: {
                    header: function (html, index, node) {
                        var title = $(node).find('option:first-child').text();
                        return title;
                    }
                }
            }
        }, {
            extend: 'csvHtml5',
            title: approve_file,
            exportOptions: {
                columns: <?= json_encode(array_keys(array_values($columns))) ?>,
                format: {
                    header: function (html, index, node) {
                        var title = $(node).find('option:first-child').text();
                        return title;
                    }
                }
            }
        }, {
            extend: 'excelHtml5',
            title: approve_file,
            exportOptions: {
                columns: <?= json_encode(array_keys(array_values($columns))) ?>,
                format: {
                    header: function (html, index, node) {
                        var title = $(node).find('option:first-child').text();
                        return title;
                    }
                }
            }
        }, {
            extend: 'pdfHtml5',
            title: approve_file,
            exportOptions: {
                columns: <?= json_encode(array_keys(array_values($columns))) ?>,
                format: {
                    header: function (html, index, node) {
                        var title = $(node).find('option:first-child').text();
                        return title;
                    }
                }
            }
        }, {
            extend: 'print',
            title: approve_file,
            exportOptions: {
                columns: <?= json_encode(array_keys(array_values($columns))) ?>,
                format: {
                    header: function (html, index, node) {
                        var title = $(node).find('option:first-child').text();
                        return title;
                    }
                }
            },
        }
    ];
    const approve_list_table = $('#vipanelApproveTable').DataTable(registerManagementDatatableOptions);
</script>