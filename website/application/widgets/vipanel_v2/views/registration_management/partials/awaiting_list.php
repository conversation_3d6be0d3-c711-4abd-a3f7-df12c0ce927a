<div class="table-responsive mt-3" style="overflow-x: auto;">
    <table class="table text-nowrap table-bordered" id="vipanelAwaitingApprovalTable">
        <thead>
            <tr>
                <?php foreach ($columns as $key => $column): ?>
                    <th><?= $registration_mapping[$key] ?? $column ?></th>
                <?php endforeach; ?>
                <th>Form Name</th>
                <th>Form ID</th>
                <th>Created At</th>
                <th>Verify</th>
            </tr>
        </thead>
        <tbody>
            <?php
            foreach ($register_list as $j => $item):
                $data = json_decode($item->data ?? "", true);
                unset($data['form_name'], $data['form_id'], $data['create_date']);

                if (!isset($data['confirmation'])):
            ?>
                    <tr id="awaiting-row-<?= $j ?>">
                        <?php foreach ($columns as $key => $column): ?>
                            <td><?= $data[$key] ?? '' ?></td>
                        <?php endforeach; ?>
                        <td>
                            <?= $item->form_name ?>
                        </td>
                        <td>
                            <?php if (!empty($item->form_id)): ?>
                                <a class="btn btn-sm btn-outline-primary btn-sm" onclick="streamDetail.load('<?= $register_list[$i]->form_id ?>')">
                                    <?= $item->form_id ?>
                                </a>
                            <?php else: ?>
                                -
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php
                            $create_date = new DateTime('now', new DateTimeZone($this->site_timezone));
                            $create_date->setTimestamp(strtotime($item->create_date));
                            ?>
                            <?= $create_date->format($site_date_format); ?>
                        </td>
                        <td>
                            <a class="btn btn-success btn-accept btn-sm"
                               onclick="registerManagement.add('<?= $data['email'] ?>', 'approve', 'awaiting-row-<?= $j ?>', this)">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-check"
                                     width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor"
                                     fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                                    <path d="M5 12l5 5l10 -10" />
                                </svg>
                            </a>
                            <a class="btn btn-danger btn-accept btn-sm"
                               onclick="registerManagement.add('<?= $data['email'] ?>', 'decline', 'awaiting-row-<?= $j ?>', this)">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-x"
                                     width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor"
                                     fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                                    <path d="M18 6l-12 12" />
                                    <path d="M6 6l12 12" />
                                </svg>
                            </a>
                        </td>

                    </tr>
                <?php endif; ?>
            <?php endforeach; ?>
        </tbody>
    </table>
</div>

<script defer>
    const awaiting_file = host + '_verification_' + fileDateSuffix;
    registerManagementDatatableOptions.buttons = [{
        extend: 'copyHtml5',
        title: awaiting_file,
        exportOptions: {
            columns: <?= json_encode(array_keys(array_values($columns))) ?>,
            format: {
                header: function(html, index, node) {
                    var title = $(node).find('option:first-child').text();
                    return title;
                }
            }
        }
    }, {
        extend: 'csvHtml5',
        title: awaiting_file,
        exportOptions: {
            columns: <?= json_encode(array_keys(array_values($columns))) ?>,
            format: {
                header: function(html, index, node) {
                    var title = $(node).find('option:first-child').text();
                    return title;
                }
            }
        }
    }, {
        extend: 'excelHtml5',
        title: awaiting_file,
        exportOptions: {
            columns: <?= json_encode(array_keys(array_values($columns))) ?>,
            format: {
                header: function(html, index, node) {
                    var title = $(node).find('option:first-child').text();
                    return title;
                }
            }
        }
    }, {
        extend: 'pdfHtml5',
        title: awaiting_file,
        exportOptions: {
            columns: <?= json_encode(array_keys(array_values($columns))) ?>,
            format: {
                header: function(html, index, node) {
                    var title = $(node).find('option:first-child').text();
                    return title;
                }
            }
        }
    }, {
        extend: 'print',
        title: awaiting_file,
        exportOptions: {
            columns: <?= json_encode(array_keys(array_values($columns))) ?>,
            format: {
                header: function(html, index, node) {
                    var title = $(node).find('option:first-child').text();
                    return title;
                }
            }
        },
    }];

    const awaiting_list_table = $('#vipanelAwaitingApprovalTable').DataTable(registerManagementDatatableOptions);
</script>