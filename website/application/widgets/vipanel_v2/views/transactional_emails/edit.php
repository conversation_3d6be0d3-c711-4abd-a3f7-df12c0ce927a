<?php
$isDark = $this->session->userdata('vipanel_theme') == 'dark';
$send_to = json_decode($detail->send_to ?? "[]");
$email_config = json_decode($detail->email_config ?? "[]");

$list_ids = $send_to->list_ids ?? [];

$temp_content = $detail->temp_content;
preg_match('/TEMPLATE:(\d+)/', $temp_content, $matches);
$template_id = $matches[1] ?? 0;

$form_names = get_form_names(site_config('project_id'));

$unsubscribe_url = $email_config->custom_unsubscribe_url ?? ('https://' . site_config('url') . '/unsubscribe');
$parsed_url = parse_url($unsubscribe_url);
parse_str($parsed_url['query'] ?? "", $query_params);

$form_name = $query_params['form_name'] ?? null;
?>
<div class="form-container">
    <form id="edit-mailing-form" onsubmit="sendgrid.save(); return false">
        <div class="card">
            <div class="card-header">
				<?php if ($detail->status == 'triggered'): ?>
                    <div class="d-flex justify-content-between align-items-center">
                        <p class="mb-0">
                            <strong class="text-success">
                                This email is sent at
								<?=
								edit_timezone($detail->send_at, project_config_default('site_timezone', 'Europe/Istanbul'))
									->format($dateTimeFormat);
								?>
                            </strong>
                            You can check the statistics of this email from the right side.
                        </p>
                        <a href="/vipanel/v2/transactional-emails/<?= $id ?>/statistic"
                           class="btn btn-warning btn-sm">
                            <i class="fa fa-chart-line"></i>
                            View Statistics
                        </a>
                    </div>
				<?php elseif ($detail->status != 'draft'): ?>
                    <p class="mb-0">
                        <strong class="text-danger">
                            This email is scheduled to be sent at
							<?=
							edit_timezone($detail->send_at, project_config_default('site_timezone', 'Europe/Istanbul'))
								->format($dateTimeFormat);
							?>
                        </strong>
                        You can't make any changes in this email. If you want to make changes, please cancel the
                        schedule.
                    </p>
				<?php else: ?>
                    <p class="mb-0">
                        <strong class="text-warning">This email is in draft mode.</strong>
                        You can make any changes in this email. If you want to publish this email, please click the
                        Publish button.
                    </p>
				<?php endif; ?>
            </div>
            <div class="card-body">
                <div class="d-flex" style="gap: 3rem">
                    <div class="w-100" id="config-side">
                        <h5 class="mb-3 border-bottom py-2">E-mail Configuration</h5>
                        <div class="mb-3">
                            <label for="name" class="form-label">
                                Title
                            </label>
                            <div class="input-group">
                        <span class="input-group-text bg-light" style="width: 48px">
                            <i class="fa fa-edit"></i>
                        </span>
                                <input type="text" class="form-control" id="name" name="name"
                                       placeholder="Enter your name"
                                       required
                                       onchange="sendgrid.email_name = this.value; sendgrid.setUnsubscribe()"
                                       value="<?= $detail->name ?>">
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <label for="send_at" class="form-label">Send At</label>
                                    <button type="button" class="btn btn-light-info btn-sm text-info p-0"
                                            data-bs-container="body" data-bs-toggle="popover" data-bs-placement="top"
                                            data-bs-trigger="focus"
                                            data-bs-content='If you want to send the email immediately, leave this field empty.'>
                                        <i class="ti ti-question-mark fs-4"></i>
                                    </button>
                                </div>
                                <div class="d-flex gap-1 justify-content-end align-items-center">
                                    <span class="form-check" for="send_now">Send Now</span>
                                    <input type="checkbox" class="form-check-input" id="send_now" name="send_now"
                                           value="1" <?= empty($detail->send_at) ? 'checked' : '' ?>>
                                </div>
                            </div>
                            <div class="input-group">
                                <span class="input-group-text bg-light" style="width: 48px">
                                    <i class="fa fa-calendar"></i>
                                </span>
                                <input type="datetime-local" class="form-control" id="send_at" name="send_at"
                                       value="<?= $detail->send_at ?>" <?= empty($detail->send_at) ? 'readonly' : '' ?>
                                       style="<?= empty($detail->send_at) ? 'background-color: #f8f9fa' : '' ?>">
                            </div>
                            <small class="d-flex gap-1 text-muted mt-1">
                                <span><b>Site Timezone:</b> <?= project_config_default('site_timezone', 'Europe/Istanbul') ?></span>
                                <button type="button" class="btn btn-light-info btn-sm text-info p-0"
                                        data-bs-container="body" data-bs-toggle="popover" data-bs-placement="top"
                                        data-bs-trigger="focus"
                                        data-bs-content='You can change site timezone from the right top corner.'>
                                    <i class="ti ti-question-mark fs-4"></i>
                                </button>
                            </small>
                        </div>

                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <label for="list_ids" class="form-label">
                                    Recipient List
                                </label>
                                <small class="text-muted">
                                    Last update:
									<?=
									empty($last_update['list']) ? 'N/A' :
										edit_timezone(
											date('Y-m-d H:i:s', $last_update['list']),
											project_config_default('site_timezone', 'Europe/Istanbul')
										)->format($dateTimeFormat);
									?>
                                </small>
                            </div>
                            <div class="input-group">
                                <span class="input-group-text bg-light" style="width: 48px; padding: 0 16px;">
                                    <i class="fa fa-list-ul"></i>
                                </span>
                                <select class="form-select select2" id="list_ids" name="list_ids">
                                    <option value="">Select a List</option>
									<?php foreach ($lists as $list): ?>
                                        <option value="<?= $list->sendgrid_id ?>" <?= in_array($list->sendgrid_id, $list_ids) ? 'selected' : '' ?>>
											<?= $list->name ?>
                                            (<?= $list->contact_count ?> contacts)
											<?php if (!empty($list->last_update_time)): ?>
                                                -
                                                (Last Action: <?=
												edit_timezone(
													$list->last_update_time,
													project_config_default('site_timezone', 'Europe/Istanbul')
												)->format($dateTimeFormat)
												?>)
											<?php endif; ?>
                                        </option>
									<?php endforeach; ?>
                                </select>
                                <a href="/vipanel/v2/transactional-emails/contacts" target="_blank"
                                   class="btn btn-warning"
                                   style="width: 48px; padding: 0 16px;">
                                    <i class="fa fa-edit"></i>
                                </a>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <label for="sender_id" class="form-label">
                                    Sender
                                </label>
                                <small class="text-muted">
                                    Last update:
									<?=
									empty($last_update['senders']) ? 'N/A' :
										edit_timezone(
											date('Y-m-d H:i:s', $last_update['senders']),
											project_config_default('site_timezone', 'Europe/Istanbul')
										)->format($dateTimeFormat);
									?>
                                </small>
                            </div>
                            <div class="input-group">
                                <span class="input-group-text bg-light" style="width: 48px; padding: 0 16px;">
                                    <i class="fa fa-envelope"></i>
                                </span>
                                <select class="form-select sender-select2" id="sender_id" name="sender_id" required>
                                    <option value="">Select a Sender</option>
									<?php foreach ($senders as $sender): ?>
                                        <option value="<?= $sender->id ?>" <?= $sender->id == $email_config->sender_id ? 'selected' : '' ?>>
											<?= $sender->from['name'] ?> (<?= $sender->from['email'] ?>)
                                        </option>
									<?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <label for="subject" class="form-label">Email Subject</label>
                            <div class="input-group">
                                <span class="input-group-text bg-light" style="width: 48px">
                                    <i class="fa fa-envelope-open"></i>
                                </span>
                                <input type="text" class="form-control" id="subject" name="subject"
                                       placeholder="Enter email subject"
                                       value="<?= $email_config->subject ?>">
                            </div>
                        </div>
                    </div>
                    <div class="w-100" id="content-side">
                        <h5 class="mb-3 border-bottom py-2">E-mail Content</h5>
                        <div class="d-flex gap-3">
                            <div class="form-group mb-3" style="width: 100%">
                                <label class="form-label">Unsubscribe Form</label>
                                <div>
                                    <select class="form-select" name="form_name"
                                            onchange="sendgrid.setUnsubscribe()">
                                        <option value="">All Forms</option>
										<?php foreach ($form_names as $name): ?>
                                            <option value="<?= $name ?>" <?= $name == $form_name ? 'selected' : '' ?>><?= $name ?></option>
										<?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group mb-3" style="width: 100%">
                                <label class="form-label">Unsubscribe Link</label>
                                <input type="text" class="form-control no-warning" name="unsubscribe_link"
                                       id="unsubscribe_link" readonly>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between align-items-center">
                            <label class="form-label">
                                E-mail Content
                            </label>
                            <button type="button" class="btn btn-light-info btn-sm text-info p-0"
                                    data-bs-container="body" data-bs-toggle="popover" data-bs-placement="top"
                                    data-bs-trigger="focus"
                                    data-bs-content='If you want to use a template, please select a template from the list. If you want to create a custom email, please select the Manual Content tab.'>
                                <i class="ti ti-question-mark fs-4"></i>
                            </button>
                        </div>
                        <ul class="nav nav-pills flex-column flex-sm-row mb-3" id="dataTabs" role="tablist">
                            <li class="nav-item mb-2 text-sm-center" role="presentation">
                                <a class="nav-link <?= !empty($template_id) ? 'active' : '' ?>" id="entry-tab"
                                   data-bs-toggle="tab"
                                   data-bs-target="#select-template"
                                   onclick="sendgrid.changeContentType()"
                                   href="javascript:;" role="tab" aria-controls="entry" aria-selected="true">
                                    Saved Website Emails
                                </a>
                            </li>
                            <li class="nav-item mb-2 text-sm-center" role="presentation">
                                <a class="nav-link <?= empty($template_id) ? 'active' : '' ?>" id="manual-content"
                                   data-bs-toggle="tab" data-bs-target="#table"
                                   href="javascript:;"
                                   onclick="sendgrid.changeContentType()"
                                   role="tab" aria-controls="table" aria-selected="false">
                                    Email Templates
                                </a>
                            </li>
                        </ul>

                        <div class="tab-content border rounded p-4 mb-3" id="dataTabsContent">
                            <div class="tab-pane fade <?= !empty($template_id) ? 'show active' : '' ?>"
                                 id="select-template" role="tabpanel"
                                 aria-labelledby="entry-tab">
                                <div class="mb-2">
                                    <div class="d-flex justify-content-between">
                                        <label for="template_id" class="form-label">
                                            Email Template
                                        </label>
                                    </div>
                                    <div class="input-group">
                                    <span class="input-group-text bg-light" style="width: 48px; padding: 0 16px;">
                                        <i class="fa fa-envelope"></i>
                                    </span>
                                        <select class="form-select select2" id="template_id" name="template_id">
                                            <option value="">Select a Template</option>
											<?php foreach ($templates ?? [] as $template): ?>
                                                <option value="<?= $template->id ?>"
                                                        data-url="<?= $template->url ?>" <?= $template->id == $template_id ? 'selected' : '' ?>>
													<?= $template->name ?>
                                                </option>
											<?php endforeach; ?>
                                        </select>

										<?php if ($detail->status != 'triggered'): ?>
                                            <button class="btn btn-warning btn-sm" type="button"
                                                    onclick="template.show()">
                                                <i class="fa fa-pencil-alt"></i>
                                            </button>
										<?php endif; ?>
                                        <button class="btn btn-secondary btn-sm" type="button"
                                                onclick="sendgrid.preview()">
                                            <i class="fa fa-eye"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="tab-pane fade <?= empty($template_id) ? 'show active' : '' ?>" id="table"
                                 role="tabpanel" aria-labelledby="manual-content">
                                <div class="mb-2">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <label for="content" class="form-label">Email Content</label>
                                        <div>
                                            <button class="btn btn-info btn-sm" type="button" id="preview_editor">
                                                <i class="fa fa-eye"></i>
                                                Preview
                                            </button>
                                            <button class="btn btn-warning btn-sm" type="button" id="expand_editor">
                                                <i class="fa fa-expand"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div id="content"></div>

                                    <div class="mt-3">
                                        <h6>Email Templates:</h6>
                                        <select name="transactional_template" id="transactional_template" class="form-select">
                                            <option value="">Select a Email Template</option>
                                            <?php foreach ($transactionalTemplates as $transactionalTemplate): ?>
                                                <option value="<?= $transactionalTemplate->id ?>"><?= $transactionalTemplate->title ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>

                                    <div class="mt-3">
                                        <h6>Variables</h6>
                                        <ul class="nav nav-tabs gap-3" id="myTab" role="tablist">
                                            <li class="nav-item" role="presentation">
                                                <button class="btn btn-outline-info" type="button"
                                                        onclick="template.copyText('{{email}}')">
                                                    {{email}}
                                                </button>
                                            </li>
                                            <li class="nav-item" role="presentation">
                                                <button class="btn btn-outline-info" type="button"
                                                        onclick="template.copyText('{{first_name}}')">
                                                    {{first_name}}
                                                </button>
                                            </li>
                                            <li class="nav-item" role="presentation">
                                                <button class="btn btn-outline-info" type="button"
                                                        onclick="template.copyText('{{last_name}}')">
                                                    {{last_name}}
                                                </button>
                                            </li>
                                            <li class="nav-item" role="presentation">
                                                <button class="btn btn-outline-info" type="button"
                                                        onclick="template.copyText(sendgrid.getUnsubscribeLink())">
                                                    Unsubscribe Link
                                                </button>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
			<?php if ($detail->status != 'triggered'): ?>
                <div class="card-footer d-flex justify-content-between">
                    <div>
                        <button type="submit" class="btn btn-outline-primary save-btn">
                            <i class="fa fa-save"></i>
                            Save as Draft
                        </button>
                        <a class="btn btn-warning" href="javascript:;" data-bs-toggle="modal"
                           data-bs-target="#modal-send-test-email">
                            <i class="fa fa-paper-plane"></i>
                            Send Test E-mail
                        </a>
                    </div>
                    <div>
						<?php if ($detail->status == 'draft'): ?>
                            <a class="btn btn-success" href="javascript:;"
                               onclick="sendgrid.publish()">
                                <i class="fa fa-share"></i>
                                Publish
                            </a>
                            <a class="btn btn-danger" href="javascript:;"
                                onclick="sendgrid.deleteSingleSend()">
                                <i class="fa fa-trash"></i>
                                Delete
                            </a>
						<?php elseif ($detail->status == 'scheduled'): ?>
                            <a class="btn btn-danger" href="javascript:;"
                               onclick="sendgrid.cancelSchedule()">
                                <i class="fa fa-times"></i>
                                Cancel Schedule
                            </a>
						<?php endif; ?>
                    </div>
                </div>
			<?php endif; ?>
        </div>
    </form>
</div>

<?php $this->load->view('transactional_emails/partials/send_test_email_modal') ?>
<?php $this->load->view('transactional_emails/partials/preview_email_modal') ?>
<?php $this->load->view('transactional_emails/partials/edit_template_modal') ?>
<?php $this->load->view('transactional_emails/partials/add_sender_modal') ?>

<script src="https://cdnjs.cloudflare.com/ajax/libs/ace/1.36.5/ace.js"
        integrity="sha512-P4Yyv1mPQ0p5qLeBwhXMt4H9xfTnIdV1E/TdNyIS3omReUnnIVePciH+456UvnW6fB2jph8MAw7ThFOT1aBahQ=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script>
    $(document).ready(function () {
        editor.init();
        sendgrid.init()

		<?php if ($detail->status != 'draft'): ?>
        $('input, select, textarea, .save-btn').prop('disabled', true);
		<?php endif; ?>
    })

    const editor = {
        instance: null,
        expend: false,
        content: `<?= empty($template_id) ? $email_config->html_content : '' ?>`,
        newWindow: null,
        init: function () {
            $('textarea[name="content"]').html(this.content);
            const editor = ace.edit("content");
            editor.setValue(this.content);
            editor.setTheme("ace/theme/monokai");
            editor.session.setMode("ace/mode/php");
            editor.setOptions({
                fontSize: "14px",
                showLineNumbers: true,
                showGutter: true,
                wrap: true
            });

            editor.session.on('change', function () {
                sendgrid.is_saved = false;
                var label = $('#content').parent().find('label');
                if (label.find('.text-danger').length === 0) {
                    label.append('<sup class="text-danger">* Unsaved</sup>')
                }
                editor.content = editor.getValue();

                if (editor.newWindow && !editor.newWindow.closed) {
                    editor.newWindow.document.body.innerHTML = editor.content;
                }
            });

            $('#preview_editor').click(function () {
                if (!editor.newWindow || editor.newWindow.closed) {
                    editor.newWindow = window.open();
                    editor.newWindow.document.open();
                    editor.newWindow.document.write(editor.getValue());
                    editor.newWindow.document.close();
                } else {
                    editor.newWindow.focus();
                }
            });

            $('#expand_editor').click(function () {
                const config = $('#config-side');
                if (!editor.expend) {
                    config.parent().addClass('flex-column');
                    $(this).find('i').removeClass('fa-expand').addClass('fa-compress');
                } else {
                    config.parent().removeClass('flex-column');
                    $(this).find('i').removeClass('fa-compress').addClass('fa-expand');
                }
                editor.expend = !editor.expend;
            });
        }
    }

    const sendgrid = {
        form: $('form#edit-mailing-form'),
        is_saved: true,
        test_emails: [],
        email_template: null,
        email_name: '<?= $detail->name ?>',
        unsubscribe_link: "<?= 'https://' . site_config('url') . '/unsubscribe' ?>",
        timezone: '<?= project_config_default('site_timezone', 'Europe/Istanbul') ?>',
        init: function () {
            this.senderSearch();
            $('#edit-mailing-form').find('input, select, textarea').not('.no-warning').on('change', function () {
                sendgrid.is_saved = false;
                var label = $(this).parent().parent().find('label');
                if (label.find('.text-danger').length === 0) {
                    label.append('<sup class="text-danger">* Unsaved</sup>')
                }
            });

            this.setUnsubscribe();

            $('#template_id').change(function () {
                sendgrid.email_template = $(this).find('option:selected').data('url');
            });

            window.onbeforeunload = this.unloadPage;

			<?php if(!empty($template_id)): ?>
            this.email_template = $('#template_id').find('option:selected').data('url');
			<?php endif; ?>

            $('input#send_now').change(function () {
                const isChecked = $(this).is(':checked');
                if (isChecked) {
                    $('input#send_at').val('').prop('readonly', true).css('background-color', '#f8f9fa');
                } else {
                    $('input#send_at').prop('readonly', false).css('background-color', '#fff');
                }
            });
        },
        save: function () {
            const editor = ace.edit("content");
            const content = editor.getValue();

            var data = this.form.serializeArray();
            data.push({name: 'id', value: '<?= $id ?? "" ?>'});
            data.push({name: 'content', value: content});

            Swal.fire({
                icon: 'warning',
                title: 'Are you sure?',
                text: 'This action will save the email configurations.',
                showCancelButton: true,
                confirmButtonText: 'Yes, save it!',
                cancelButtonText: 'No, keep it',
                confirmButtonColor: '#33dd9c',
                cancelButtonColor: '#d63030',
                reverseButtons: true
            }).then(function (result) {
                if (result.isConfirmed) {
                    $('.preloader').fadeIn();
                    $.ajax({
                        type: 'POST',
                        url: '/vipanel/v2/transactional-emails/make/update_email',
                        data: data,
                        success: function (data) {
                            sendgrid.is_saved = true;
                            $('.preloader').fadeOut();
                            Swal.fire({
                                icon: data.status ? 'success' : 'error',
                                title: data.status ? 'Saved successfully!' : 'Something went wrong!',
                                showConfirmButton: false,
                                timer: 3000,
                                timerProgressBar: true
                            }).then(function () {
                                if (data.status) {
                                    window.location.reload();
                                }
                            });
                        },
                        error: function (data) {
                            Swal.fire({
                                position: 'bottom-end',
                                toast: true,
                                icon: 'error',
                                title: 'Something went wrong!',
                                showConfirmButton: false,
                                timer: 3000,
                                timerProgressBar: true
                            });
                            $('.preloader').fadeOut();
                        }
                    })
                }
            })
        },
        publish: function () {
            const sendAt = $('input#send_at').val();
            if (sendAt === '' && !$('#send_now').is(':checked')) {
                return Swal.fire({
                    position: 'bottom-end',
                    toast: true,
                    icon: 'warning',
                    title: 'Please select a date and time to schedule the email!',
                    showConfirmButton: false,
                    timer: 5000,
                    timerProgressBar: true
                });
            }
            let sendAtDate = '';
            if (sendAt !== '' && !$('#send_now').is(':checked')) {
                const date = new Date(sendAt).getTime();
                const offset = new Date().getTimezoneOffset() * 60000;
                const sendAtTime = date - offset;
                sendAtDate = new Date(sendAtTime).toISOString().slice(0, 19).replace('T', ' ');
            }
            if (!sendgrid.is_saved) {
                return sendgrid.unsavedError();
            }
            Swal.fire({
                icon: 'warning',
                title: 'Are you sure?',
                text: sendAt ? ('This email will be scheduled to be sent at ' + this.timezone + ' ' + sendAtDate) : 'This email will be sent immediately.',
                showCancelButton: true,
                confirmButtonText: 'Yes, publish it!',
                cancelButtonText: 'No, keep it',
                confirmButtonColor: '#33dd9c',
                cancelButtonColor: '#d63030',
                reverseButtons: true
            }).then(function (data) {
                if (data.isConfirmed) {
                    $('.preloader').fadeIn();
                    $.ajax({
                        url: '/vipanel/v2/transactional-emails/make/publish_email',
                        method: 'POST',
                        data: {
                            id: '<?= $id ?>'
                        },
                        success: function (data) {
                            Swal.fire({
                                icon: data.status ? 'success' : 'error',
                                title: data.status ? 'Published successfully!' : data.message,
                                showConfirmButton: false,
                                timer: 5000,
                                timerProgressBar: true
                            }).then(function () {
                                window.location.reload();
                            });
                            $('.preloader').fadeOut();
                        },
                        error: function (data) {
                            Swal.fire({
                                position: 'bottom-end',
                                toast: true,
                                icon: 'error',
                                title: 'Something went wrong!',
                                showConfirmButton: false,
                                timer: 5000,
                                timerProgressBar: true
                            });
                            console.log(data);
                            $('.preloader').fadeOut();
                        }
                    })
                }
            })
        },
        deleteSingleSend: function () {
            Swal.fire({
                icon: 'warning',
                title: 'Are you sure?',
                text: 'This action will delete this email. You can not undo this action.',
                showCancelButton: true,
                confirmButtonText: 'Yes, delete it!',
                cancelButtonText: 'No, keep it',
                confirmButtonColor: '#33dd9c',
                cancelButtonColor: '#d63030',
                reverseButtons: true
            }).then(function (data) {
                if (data.isConfirmed) {
                    $('.preloader').fadeIn();
                    $.ajax({
                        url: '/vipanel/v2/transactional-emails/make/delete_single_send',
                        method: 'POST',
                        data: {
                            id: '<?= $id ?>'
                        },
                        success: function (data) {
                            Swal.fire({
                                icon: data.status ? 'success' : 'error',
                                title: data.status ? 'Deleted successfully!' : 'Something went wrong!',
                                showConfirmButton: false,
                                timer: 3000,
                                timerProgressBar: true
                            }).then(function () {
                                window.location.href = '/vipanel/v2/transactional-emails';
                            });
                            $('.preloader').fadeOut();
                        },
                        error: function (data) {
                            Swal.fire({
                                position: 'bottom-end',
                                toast: true,
                                icon: 'error',
                                title: 'Something went wrong!',
                                showConfirmButton: false,
                                timer: 3000,
                                timerProgressBar: true
                            });
                            console.log(data);
                            $('.preloader').fadeOut();
                        }
                    })
                }
            })
        },
        sendTestEmail: function () {
            const emails = this.test_emails;
            Swal.fire({
                icon: 'warning',
                title: 'Are you sure?',
                text: 'This action will send a test email to the defined email addresses.',
                showCancelButton: true,
                confirmButtonText: 'Yes, send it!',
                cancelButtonText: 'No, keep it',
                confirmButtonColor: '#33dd9c',
                cancelButtonColor: '#d63030',
                reverseButtons: true
            }).then(function (data) {
                if (data.isConfirmed) {
                    if (!sendgrid.is_saved) {
                        return sendgrid.unsavedError();
                    }
                    $('.preloader').fadeIn();
                    $.ajax({
                        url: '/vipanel/v2/transactional-emails/make/send_test_email',
                        method: 'POST',
                        data: {
                            id: '<?= $id ?>',
                            emails: emails
                        },
                        success: function (data) {
                            Swal.fire({
                                position: 'bottom-end',
                                toast: true,
                                icon: data.status ? 'success' : 'error',
                                title: data.status ? 'Test email sent successfully!' : 'Something went wrong!',
                                showConfirmButton: false,
                                timer: 3000,
                                timerProgressBar: true
                            });
                            $('.preloader').fadeOut();
                        },
                        error: function (data) {
                            Swal.fire({
                                position: 'bottom-end',
                                toast: true,
                                icon: 'error',
                                title: 'Something went wrong!',
                                showConfirmButton: false,
                                timer: 3000,
                                timerProgressBar: true
                            });
                            console.log(data);
                            $('.preloader').fadeOut();
                        }
                    })
                }
            })
        },
        cancelSchedule: function () {
            Swal.fire({
                icon: 'warning',
                title: 'Are you sure?',
                text: 'This action will cancel the schedule of this email.',
                showCancelButton: true,
                confirmButtonText: 'Yes, cancel it!',
                cancelButtonText: 'No, keep it',
                confirmButtonColor: '#33dd9c',
                cancelButtonColor: '#d63030',
                reverseButtons: true
            }).then(function (data) {
                if (data.isConfirmed) {
                    $('.preloader').fadeIn();
                    $.ajax({
                        url: '/vipanel/v2/transactional-emails/make/cancel_schedule',
                        method: 'POST',
                        data: {
                            id: '<?= $id ?>'
                        },
                        success: function (data) {
                            Swal.fire({
                                icon: data.status ? 'success' : 'error',
                                title: data.status ? 'Schedule canceled successfully!' : 'Something went wrong!',
                                showConfirmButton: false,
                                timer: 3000,
                                timerProgressBar: true
                            }).then(function () {
                                window.location.reload();
                            });
                            $('.preloader').fadeOut();
                        },
                        error: function (data) {
                            Swal.fire({
                                position: 'bottom-end',
                                toast: true,
                                icon: 'error',
                                title: 'Something went wrong!',
                                showConfirmButton: false,
                                timer: 3000,
                                timerProgressBar: true
                            });
                            console.log(data);
                            $('.preloader').fadeOut();
                        }
                    })
                }
            })
        },
        unsavedError: function () {
            return Swal.fire({
                position: 'bottom-end',
                toast: true,
                icon: 'warning',
                title: 'Unsaved changes!',
                text: 'Please save the configurations before this action!',
                showConfirmButton: false,
                timer: 5000,
                timerProgressBar: true
            });
        },
        preview: function () {
            var template = this.email_template;
            if (template === null || template === "" || template === undefined) {
                return Swal.fire({
                    position: 'bottom-end',
                    toast: true,
                    icon: 'warning',
                    title: 'Please select a template first!',
                    showConfirmButton: false,
                    timer: 5000,
                    timerProgressBar: true
                });
            }
            $('#preview_email_content').attr('src', '/' + template);
            $('#modal-preview-email-content').modal('show');
        },
        unloadPage: function () {
            if (!sendgrid.is_saved) {
                $('.preloader').fadeOut();
                return "You have unsaved changes on this page. Do you want to leave this page and discard your changes or stay on this page?";
            }
        },
        getUnsubscribeLink: function () {
            let link = this.unsubscribe_link;
            let form_name = $('select[name=form_name').val();
            link += '?email={{email}}';
            if (form_name !== undefined && form_name !== null && form_name !== "") {
                link += '&form_name=' + form_name;
            }
            link += '&source=' + btoa(this.email_name);
            return link;
        },
        setUnsubscribe: function () {
            $('input[name=unsubscribe_link').val(this.getUnsubscribeLink());
        },
        senderSearch: function () {
            $('.sender-select2').select2({
                ajax: {
                    url: '/vipanel/v2/transactional-emails/make/retrieve_senders',
                    dataType: 'json',
                    delay: 250,
                    data: function (params) {
                        return {
                            q: params.term
                        };
                    },
                    processResults: function (data) {
                        return {
                            results: data.items
                        };
                    },
                    cache: true
                },
                minimumInputLength: 1,
                templateResult: function (data) {
                    return data.text;
                },
                templateSelection: function (data) {
                    return data.text;
                }
            });

            $('.sender-select2').change(function () {
                let value = $(this).val();
                let text = $(this).find('option:selected').text();
                if (value === '-1') {
                    text = text.replace('Create a sender as "', '').replace('"', '');
                    $('#modal-add-sender').modal('show').find('input[name="name"]').val(text);

                    $(this).val("").trigger('change');
                }
            });
        },
        changeContentType: function () {
            const tab = $('#dataTabs').find('.active').attr('id');
            if (tab === 'entry-tab') {
                const editor = ace.edit("content");
                editor.setValue('');
            } else {
                $('#template_id').val('').trigger('change');
            }
        }
    }

    $(document).ready(function () {
        $('#transactional_template').change(function () {
            var templateId = $(this).val();
            if (templateId) {
                console.log(templateId);
                getEmailTemplateHtml(templateId);
            }
        });
    });

    function getEmailTemplateHtml(templateId){
        $.ajax({
            type: 'POST',
            url: '/vipanel/v2/transactional-emails/template-html',
            data: {
                id: templateId
            },
            success: function (data) {
                const editor = ace.edit("content");
                editor.setValue(data.html);
            },
            error: function (data) {
                console.log(data);
            }
        })
    }
</script>

<style>
    .input-group {
        flex-wrap: nowrap !important;
    }

    .select2-container--default .select2-selection--single {
        border-color: <?= $isDark ? '#465670' : '#dfe5ef' ?>;
    }

    #edit-editor, #content, iframe {
        height: 70vh;
        width: 100%;
    }

    .border-left {
        border-left: 1px solid<?= $isDark ? '#465670' : '#dfe5ef' ?>;
    }
</style>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.13.4/codemirror.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.13.4/theme/dracula.min.css">