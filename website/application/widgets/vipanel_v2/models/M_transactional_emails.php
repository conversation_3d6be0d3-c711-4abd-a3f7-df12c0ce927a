<?php

class M_transactional_emails extends CI_Model
{
	protected $site;

	public function __construct()
	{
		parent::__construct();
		$this->site = site_config('url');
	}
	#####################################
	########### SINGLE SENDS ############
	#####################################
	/**
	 * Get all transactional emails
	 * @return array
	 */
	public function getTransactionalEmails(): array
	{
		$data = $this->db->from('sg_sends')
			->where('project_id', site_config('project_id'))
			->get()
			->result();
		foreach ($data as $i => $item) {
			$data[$i]->categories = json_decode($item->categories);
			$data[$i]->id = $item->sendgrid_id;
		}
		return $data;
	}

	/**
	 * Get single send by ID
	 * @param string $id
	 * @return object
	 */
	public function getSingleSendById(string $id): ?object
	{
		return $this->db->from('sg_sends')
			->where('project_id', site_config('project_id'))
			->where('sendgrid_id', $id)
			->get()
			->row();
	}

	/**
	 * Get transactional emails by SendGrid IDs
	 * @param array $ids
	 * @return array
	 */
	public function getSendgridIds(array $ids, string $table = 'sg_sends'): array
	{
		if (empty($ids)) {
			return [];
		}
		return $this->db->from($table)
			->where_in('sendgrid_id', $ids)
			->select('id, sendgrid_id')
			->get()
			->result_array();
	}

	/**
	 * Save transactional emails
	 * @param array $data
	 * @return bool
	 */
	public function saveTransactionalEmails(array $data): bool
	{
		try {
			$ids = array_column($data, 'sendgrid_id');
			$exists = $this->getSendgridIds($ids) ?? [];
			$sendgridIDs = array_column($exists ?? [], 'sendgrid_id');
			$save = [
				'update' => [],
				'insert' => []
			];
			foreach ($data as $i => $item) {
				if (!empty($item->categories)) {
					$item->categories = json_encode($item->categories);
				}
				$sendgrid_id = "";
				if (!empty($item->sendgrid_id)) {
					$sendgrid_id = $item->sendgrid_id;
				} elseif (is_array($item) && !empty($item['sendgrid_id'])) {
					$sendgrid_id = $item['sendgrid_id'];
				}
				if (($n = array_search(($sendgrid_id), $sendgridIDs)) !== false) {
					if (is_object($item)) {
						$item->id = $exists[$n]['id'];
					} else {
						$item['id'] = $exists[$n]['id'];
					}
					$save['update'][$i] = (array)$item;
				} else {
					$save['insert'][$i] = (array)$item;
				}
			}
			if (!empty($save['update'])) {
				$this->db->update_batch('sg_sends', $save['update'], 'id');
			}
			if (!empty($save['insert'])) {
				$this->db->insert_batch('sg_sends', $save['insert']);
			}
			return true;
		} catch (Exception $e) {
			return false;
		}
	}

	#####################################
	############# SENDERS ###############
	#####################################

	public function getLists()
	{
		$data = $this->db->from('sg_lists')
			->where('project_id', site_config('project_id'))
			->get()
			->result();
		return $data;
	}

	public function getListById($id)
	{
		return $this->db->from('sg_lists')
			->where('project_id', site_config('project_id'))
			->where('sendgrid_id', $id)
			->get()
			->row_array();
	}

	public function getListByName($name)
	{
		return $this->db->from('sg_lists')
			->where('project_id', site_config('project_id'))
			->where('name', $name)
			->get()
			->row_array();
	}

	public function getListsByFormNames(array $form_names)
	{
		return $this->db->from('sg_lists')
			->where('project_id', site_config('project_id'))
			->where_in('related_form', $form_names)
			->get()
			->result();
	}

	public function saveLists($data): bool
	{
		try {
			$ids = array_column($data, 'sendgrid_id');
			$exists = $this->getSendgridIds($ids, 'sg_lists') ?? [];
			$sendgridIDs = array_column($exists ?? [], 'sendgrid_id');
			$save = [
				'update' => [],
				'insert' => []
			];

			foreach ($data as $i => $item) {
				$sendgrid_id = "";
				if (!empty($item->sendgrid_id)) {
					$sendgrid_id = $item->sendgrid_id;
				} elseif (!empty($item['sendgrid_id'])) {
					$sendgrid_id = $item['sendgrid_id'];
				}
				if (($n = array_search(($sendgrid_id), $sendgridIDs)) !== false) {
					if (is_object($item)) {
						$item->id = $exists[$n]['id'];
					} else {
						$item['id'] = $exists[$n]['id'];
					}
					$save['update'][$i] = (array)$item;
				} else {
					$save['insert'][$i] = (array)$item;
				}
			}
			if (!empty($save['update'])) {
				$this->db->update_batch('sg_lists', $save['update'], 'id');
			}
			if (!empty($save['insert'])) {
				$this->db->insert_batch('sg_lists', $save['insert']);
			}
			return true;
		} catch (Exception $e) {
			return false;
		}
		return true;
	}

	public function updateList($id, $data)
	{
		return $this->db->where('sendgrid_id', $id)
			->update('sg_lists', $data);
	}

	public function deleteList($id)
	{
		return $this->db->where('sendgrid_id', $id)
			->delete('sg_lists');
	}

	/**
	 * Get all senders
	 * @param string $search
	 * @return object
	 */
	public function getSenders(string $search = ""): array
	{
		if (!empty($search)) {
			$data = $this->db->from('sg_senders')
				->like('lower(sg_senders.from)', strtolower($search))
				->get()
				->result();
		} else {
			$data = $this->db->from('sg_senders')
				->join('sg_senders_project', 'sg_senders.id = sg_senders_project.sender_id', 'inner')
				->where('sg_senders_project.project_id', site_config('project_id'));
			$data = $data->get()->result();
			if (empty($data)) {
				$data = $this->db->from('sg_senders')
					->get()
					->result();
			}
		}
		foreach ($data as $i => $item) {
			$data[$i]->address = json_decode($item->address, true);
			$data[$i]->reply_to = json_decode($item->reply_to, true);
			$data[$i]->from = json_decode($item->from, true);
			$data[$i]->verified = json_decode($item->verified, true);
		}
		return $data;
	}

	/**
	 * Get all senders
	 * @param array $data
	 * @return bool
	 */
	public function saveSenders(array $data): bool
	{
		try {
			foreach ($data as $i => $item) {
				foreach ($item as $key => $value) {
					if (is_object($value) || is_array($value)) {
						$data[$i]->{$key} = json_encode($value);
					} else {
						$data[$i]->{$key} = $value;
					}
				}
			}
			$this->db->truncate('sg_senders');
			$this->db->insert_batch('sg_senders', $data);
			return true;
		} catch (Exception $e) {
			return false;
		}
	}

	#####################################
	######### CONTACTS & LISTS ##########
	#####################################
	/**
	 * Save contact
	 * @param string $id
	 * @param array $export_urls
	 * @param bool $is_export_waiting
	 * @return bool
	 */
	public function saveContactExports(string $id, array $export_urls, bool $is_export_waiting = true): bool
	{
		return $this->db->where('sendgrid_id', $id)
			->update('sg_lists', [
				'is_export_waiting' => $is_export_waiting,
				'export_urls' => json_encode($export_urls),
				'last_update_time' => date('Y-m-d H:i:s')
			]);
	}

	public function saveExportedContacts(array $data)
	{
		if (empty($data)) {
			return false;
		}
		$this->db->truncate('sg_contacts');
		return $this->db->insert_batch('sg_contacts', $data);
	}

	/**
	 * Get contact exporting
	 * @return array
	 */
	public function getContactExporting(): array
	{
		return $this->db->from('sg_lists')
			->where('is_export_waiting', 1)
			->get()
			->result();
	}

	/**
	 * Get list by form name
	 *
	 * @param array $formNames
	 * @return array
	 */
	public function getRelatedLists(array $formNames): array
	{
		return $this->db->from('sg_lists')
			->where('project_id', site_config('project_id'))
			->group_start()
			->where_in('related_form', $formNames)
			->or_where('related_form', null)
			->group_end()
			->get()
			->result();
	}

	/**
	 * Get unsubscribe list
	 * @return array
	 */
	public function getUnsubscribeList(): array
	{
		return $this->db->from('unsubscribes')
			->where('project_id', site_config('project_id'))
			->get()
			->result();
	}

	#####################################
	############### QUEUE ###############
	#####################################

	/**
	 * Get contact exporting by ID
	 * @param string $related_id
	 * @return array
	 */
	public function getQueueList(string $related_id, string $name = ''): array
	{
		return $this->db->from('sg_queue')
			->where('project_id', site_config('project_id'))
			->where('name', $name)
			->group_start()
			->where('related_id', $related_id)
			->or_where('related_id', null)
			->group_end()
			->get()
			->result();
	}

	/**
	 * Get contact exporting by ID
	 * @param string $job_id
	 * @return object
	 */
	public function getQueueItemByJobId(string $job_id): object
	{
		return $this->db->from('sg_queue')
			->where('job_id', $job_id)
			->get()
			->row();
	}

	/**
	 * Get contact exporting by related ID
	 * @param string $sendgrid_id
	 * @return array
	 */
	public function getQueueItemByRelatedId(string $sendgrid_id): array
	{
		return $this->db->from('sg_queue')
			->where('related_id', $sendgrid_id)
			->or_where('related_id', null)
			->get()
			->result();
	}

	/**
	 * Update contact queue status
	 * @param string $id
	 * @param array $data
	 * @return bool
	 */
	public function updateQueueItem(string $id, array $data): bool
	{
		return $this->db->where('job_id', $id)
			->update('sg_queue', $data);
	}

	/**
	 * Save contact queue status
	 * @param array $data
	 * @return bool
	 */
	public function saveQueueItem(array $data): bool
	{
		return $this->db->insert('sg_queue', $data);
	}

	/**
	 * Retrieve register list
	 * @param string|null $form_name
	 * @return array
	 */
	public function retrieveRegister(string $form_name = ''): array
	{
		$data = $this->db->from('register')
			->where('project_id', site_config('project_id'));
		if (!empty($form_name)) {
			$data->where('form_name', $form_name);
		}
		$data = $data->get();
		return $data ? $data->result() : [];
	}

	/**
	 * Retrieve template
	 * @param string $template
	 * @return array
	 */
	public function retrieveTemplate(string $template): object
	{
		$data = $this->db->from('page')
			->where('project_id', site_config('project_id'))
			->where('url', $template)
			->get();
		return $data ? $data->row() : [];
	}

	/**
	 * Save template
	 * @param int $id
	 * @param string $content
	 * @return bool
	 */
	public function saveTemplate(int $id, string $content): bool
	{
		return $this->db->where('id', $id)
			->where('project_id', site_config('project_id'))
			->update('page', [
				'content' => $content
			]);
	}

	/**
	 * Retrieve reminder list
	 * @param string|null $form_name
	 * @return array
	 */
	public function retrieveReminderList(string $form_name = ''): array
	{
		$sql = 'select
	replace(JSON_EXTRACT(data, \'$.email\'), \'"\', \'\') as email,
	replace(JSON_EXTRACT(data, \'$.fullname\'), \'"\', \'\') as fullname,
	replace(JSON_EXTRACT(data, \'$.first_name\'), \'"\', \'\') as first_name,
	replace(JSON_EXTRACT(data, \'$.last_name\'), \'"\', \'\') as last_name
from
	register
where
	project_id = ' . site_config('project_id') . '
	' . (!empty($form_name) ? 'and form_name = "' . $form_name . '"' : '');
		return $this->db->query($sql)->result();
	}

	/**
	 * Retrieve thank you list
	 * @param string|null $form_name
	 * @return array
	 */
	public function retrieveThankYouList(string $form_name = ''): array
	{
		$sql = 'select
	replace(JSON_EXTRACT(data, \'$.email\'), \'"\', \'\') as email,
	replace(JSON_EXTRACT(data, \'$.fullname\'), \'"\', \'\') as fullname,
	replace(JSON_EXTRACT(data, \'$.first_name\'), \'"\', \'\') as first_name,
	replace(JSON_EXTRACT(data, \'$.last_name\'), \'"\', \'\') as last_name
from
	register
where
	project_id = ' . site_config('project_id') . '
	' . (!empty($form_name) ? 'and form_name = "' . $form_name . '"' : '') . '
	and replace(JSON_EXTRACT(data, \'$.email\'), \'"\', \'\') in (
	select
		email
	from
		tracked_time tt
	where
		project_id = ' . site_config('project_id') . '
	)';
		return $this->db->query($sql)->result();
	}

	/**
	 * Retrieve miss you list
	 * @param string|null $form_name
	 * @return array
	 */
	public function retrieveMissYouList(string $form_name = ''): array
	{
		$sql = 'select
	replace(JSON_EXTRACT(data, \'$.email\'), \'"\', \'\') as email,
	replace(JSON_EXTRACT(data, \'$.fullname\'), \'"\', \'\') as fullname,
	replace(JSON_EXTRACT(data, \'$.first_name\'), \'"\', \'\') as first_name,
	replace(JSON_EXTRACT(data, \'$.last_name\'), \'"\', \'\') as last_name
from
	register
where
	project_id = ' . site_config('project_id') . '
	' . (!empty($form_name) ? 'and form_name = "' . $form_name . '"' : '') . '
	and replace(JSON_EXTRACT(data, \'$.email\'), \'"\', \'\') not in (
	select
		email
	from
		tracked_time tt
	where
		project_id = ' . site_config('project_id') . '
	)';
		return $this->db->query($sql)->result();
	}

	public function deleteSingleSendById(string $id = ''): bool
	{
		if (empty($id)) {
			return false;
		}
		return $this->db->where('sendgrid_id', $id)
			->delete('sg_sends');
	}

	public function retrieveUnusedContacts()
	{
		$subquery = $this->db->select("REPLACE(JSON_EXTRACT(data, '$.email'), '\"', '') as email")
			->from('register')
			->get_compiled_select();

		$this->db->select('c.email, c.contact_id')
			->from('sg_contacts c')
			->join('sg_lists l', 'l.sendgrid_id = c.list_id', 'left')
			->join('project p', 'p.id = l.project_id', 'left')
			->where('p.url IS NULL')
			->where("c.email NOT IN ($subquery)", null, false);

		return $this->db->get()->result();
		/*
			select
				c.email, c.contact_id
			from
				sg_contacts c
			left join sg_lists l on l.sendgrid_id = c.list_id
			left join project p on p.id = l.project_id
			where p.url is null and c.email not in (select replace(json_extract(data, '$.email'), '"', '') as email from register)
		*/
	}
}
