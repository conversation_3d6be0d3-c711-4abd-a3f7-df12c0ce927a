<?php

class M_import extends CI_Model
{
    public function getAfmeUsers($postData)
    {
        $response = array();

        ## Read value
        $draw = isset($postData['draw']) ? $postData['draw'] : 1;
        $start = $postData['start'];
        $rowperpage = $postData['length']; // Rows display per page
        $columnIndex = $postData['order'][0]['column']; // Column index
        $columnName = $postData['columns'][$columnIndex]['data']; // Column name
        $columnSortOrder = $postData['order'][0]['dir']; // asc or desc
        $searchValue = $postData['search']['value']; // Search value

        ## Search
        $searchQuery = "";
        if ($searchValue != '') {
            $searchQuery = " (hcp_id like '%" . $searchValue . "%' or email like '%" . $searchValue . "%' or speciality like'%" . $searchValue . "%' or country like'%" . $searchValue . "%' ) ";
        }

        ## Total number of records without filtering
        $this->db->select('count(*) as allcount');
        $records = $this->db->get('afme_users')->result();
        $totalRecords = $records[0]->allcount;

        ## Total number of record with filtering
        $this->db->select('count(*) as allcount');
        if ($searchQuery != '')
            $this->db->where($searchQuery);
        $records = $this->db->get('afme_users')->result();
        $totalRecordwithFilter = $records[0]->allcount;

        ## Fetch records
        $this->db->select('*');
        if ($searchQuery != '')
            $this->db->where($searchQuery);
        $this->db->order_by($columnName, $columnSortOrder);
        $this->db->limit($rowperpage, $start);
        $records = $this->db->get('afme_users')->result();

        $data = array();

        foreach ($records as $record) {

            $data[] = array(
                "email" => $record->email,
                "country" => $record->country,
            );
        }

        ## Response
        $response = array(
            "draw" => intval($draw),
            "iTotalRecords" => $totalRecords,
            "iTotalDisplayRecords" => $totalRecordwithFilter,
            "aaData" => $data
        );

        return $response;
    }

    public function importAfmeUsers($data)
    {
        $this->db->truncate('afme_users');
        $chunks = array_chunk($data, 1000);
        foreach ($chunks as $chunk) {
            $res = $this->db->insert_batch('afme_users', $chunk);
            if (!$res) {
                return FALSE;
            }
        }
        return TRUE;
    }
}