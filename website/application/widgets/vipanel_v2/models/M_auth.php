<?php

class M_auth extends CI_Model
{
	public function check_user($project_id, $email, $password)
	{
		$user = $this->db->from('vipanel_users')
			->where([
				'project_id' => $project_id,
				'email' => $email
			])->get()->row();
		if ($user) {
			if (password_verify($password, $user->password)) {
				$data = [
					'user_id' => $user->id,
					'fullname' => $user->fullname,
					'email' => $user->email,
					'country' => $user->country,
					'company' => $user->company,
					'is_admin' => false,
					'permissions' => json_decode($user->permissions),
				];
				$this->session->set_userdata([
					'site_vipanel' => $data
				]);
				$vipanel_internal_user = ['vistream.tv', 'vidizayn.com', 'niceye.com'];
				$email_domain = explode('@', $email)[1];
				if (in_array($email_domain, $vipanel_internal_user)) {
					$this->session->set_userdata('vipanel_internal_user', true);
				}
				return true;
			}
		}
		$user = $this->db->from('users')
			->where("project_id", $project_id)
			->where("username", $email)
			->where("type", 40)
			->or_group_start()
			->where("username", $email)
			->where("type", 41)
			->group_end()
			->or_group_start()
			->where("username", $email)
			->where("type", 50)
			->group_end()
			->get()->row();
		if ($user) {
			if (password_verify($password, $user->password)) {
				$domain = explode('@', $user->username);
				$domain = $domain[1] ?? null;
				if ($domain) {
					$domain = explode('.', $domain);
					$domain = $domain[0] ?? null;
				}
				$data = [
					'user_id' => $user->id,
					'fullname' => $user->full_name,
					'email' => $user->username,
					'country' => 'all',
					'company' => ucwords($domain),
					'is_admin' => true,
					'permissions' => json_decode(json_encode([
						'dashboard' => [
							"main_screen" => "on"
						],
						'livestreaming_setup' => [
							"main_screen" => "on",
							'stream_embed' => "on",
							'rtmp_url' => "on",
							'breakout_rooms' => "on",
							'backstage_setup' => "on",
							'showflow_planning' => "on"
						],
						'registration_management' => [
							"main_screen" => "on",
							'registration_list' => "on",
							'register_management' => "on",
							'p_reg_email_settings' => "on",
							'access_restrictions' => "on",
							'login_settings' => "on",
							'timezone_settings' => "on"
						],
						'interactive_tools' => [
							"main_screen" => "on",
							'qa_modules' => "on",
							'polling_modules' => "on",
							'pre_survey_setup' => "on",
							'post_survey_setup' => "on",
							'certificate_modules' => "on",
							'quiz_modules' => "on",
							'notifications' => "on",
							'announcements' => "on",
							'topic_management' => "on",
						],
						'live_webinar_reports' => [
							"main_screen" => "on",
							'attendee_report' => 'on',
							'login_report' => 'on',
							'chat_report' => 'on',
							'polling_report' => 'on',
							'survey_report' => 'on',
							'reaction_report' => 'on',
							'webinar_metrics_insights' => 'on',
						],
                            'on_demand_video' => [
                            "main_screen" => "on"
                        ],
                            'transactional_emails' => [
                            "main_screen" => "on",
                            'email_templates' => 'on',
                        ],
					])),
				];
				$this->session->set_userdata([
					'site_vipanel' => $data
				]);
				$vipanel_internal_user = ['vistream.tv', 'vidizayn.com', 'niceye.com'];
				$email_domain = explode('@', $email)[1];
				if (in_array($email_domain, $vipanel_internal_user)) {
					$this->session->set_userdata('vipanel_internal_user', true);
				}
				return true;
			}
		}
		return false;
	}

	public function get_user($project_id, $email)
	{
		return $this->db->from('vipanel_users')
			->where([
				'project_id' => $project_id,
				'email' => $email
			])->get()->row();
	}
}