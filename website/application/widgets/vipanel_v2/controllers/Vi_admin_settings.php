<?php

class Vi_admin_settings extends vipanelController
{
    protected $permissionLevels = [
        'dashboard' => [],
        'livestreaming_setup' => [
            'stream_embed',
            'rtmp_url',
            'breakout_rooms',
            'backstage_setup',
            'showflow_planning'
        ],
        'registration_management' => [
            'registration_list',
            'register_management',
            'p_reg_email_settings',
            'access_restrictions',
            'login_settings',
            'timezone_settings'
        ],
        'interactive_tools' => [
            'qa_modules',
            'polling_modules',
            'pre_survey_setup',
            'post_survey_setup',
            'certificate_modules',
            'quiz_modules',
            'notifications',
            'announcements',
            'topic_management'
        ],
        'live_webinar_reports' => [
            'attendee_report',
            'login_report',
            'chat_report',
            'polling_report',
            'survey_report',
            'webinar_metrics_insights'
        ],
        'on_demand_video' => [],
        'transactional_emails' => [
            'email_templates'
        ],
    ];
    public string $adminNotify =
    'This area is only open to emails with domain addresses <b>"vistream.tv"</b>, <b>"vidizayn.com"</b> and <b>"niceye.com"</b>.';

    public function __construct()
    {
        parent::__construct();
        $this->load->helper('admin_settings_helper');
        $this->project_id = $this->session->userdata('site_config')->project_id;
    }

    public function onDemandVideo()
    {
        $data = getOnDemandVideos();
        $categories = [
            1 => 'PAIN',
            2 => 'CNS',
            3 => 'CV',
        ];
        if (getArray('video_categories')) {
            $array = getArray('video_categories');
            $categories = array_combine(range(1, count($array)), array_values($array));
        }
        $this->template('admin_settings/on_demand_video', [
            'full_width' => true,
            'breadcrumb' => true,
            'breadcrumb_list' => [
                'Admin Settings'
            ],
            'breadcrumb_description' => $this->adminNotify,
            'title' => 'On Demand Video',
            'data' => $data,
            'categories' => $categories
        ]);
    }

    public function getReport($type = null)
    {
        $reportType = $this->input->get('report_type', true);
        if ($reportType !== null) {
            $type = $reportType;
        }
        $start_date = $this->input->get('start_date', true);
        $end_date = $this->input->get('end_date', true);

        if (!$start_date) {
            $start_date = date('Y-m-d');
        }
        if (!$end_date) {
            $end_date = date('Y-m-d');
        }
        $remove_test_users = null;
        if ($this->input->get('remove_test', true)) {
            $remove_test_users = 1;
        }

        //Hidden domains filtreleme alanı
        $hidden_domains = json_decode(vipanel_project_config('hidden_domains'), true) ?? [];
        $show_hidden_domains = $this->input->get('hidden_domains', true) == '1';

        $filterHiddenDomains = function($data) use ($hidden_domains, $show_hidden_domains) {
            if (!$show_hidden_domains && !empty($hidden_domains)) {
                return array_filter($data, function($item) use ($hidden_domains) {

                    $email = '';
                    if (is_array($item)) {
                        $email = $item['Email'] ?? $item['email'] ?? '';
                        if (empty($email) && isset($item['data']) && is_string($item['data'])) {
                            $data = json_decode($item['data'], true);
                            $email = $data['email'] ?? '';
                        }
                    } else if (is_object($item)) {
                        $email = $item->Email ?? $item->email ?? '';
                        if (empty($email) && isset($item->data) && is_string($item->data)) {
                            $data = json_decode($item->data, true);
                            $email = $data['email'] ?? '';
                        }
                    }
                    
                    $email_parts = explode('@', $email);
                    $domain = $email_parts[1] ?? null;
                    return !in_array($domain, $hidden_domains);
                });
            }
            return $data;
        };

        //Rapor filtreleme formdan gelen datalar ve filtrelemede kullanılan datalar
        $data = [];
        $columns = [];
        $is_option = false; // Kullanıcı için raporlama ekranında filtreleme seçeneklerinin olup olmayacağını belirtir.
        $time_filter = false; // Kullanıcı için raporlama ekranında tarih filtrelemesinin olup olmayacağını belirtir.
        $detailed_columns = false; // Kullanıcı için raporlama ekranında tarih filtrelemesinin olup olmayacağını belirtir.
        $select_filter = []; // Kullanıcı için raporlama ekranında ilgili raporda rapor özelinde filtreleme seçenekleri sunar.

        switch ($type) {
            case "embed_report":
                $data = getEmbedLogsAdmin(site_config('project_id'));
                $is_option = true;
                $data = $filterHiddenDomains($data);
                $columns = [
                    'id' => 'ID',
                    'log_id' => 'Log ID',
                    'project_id' => 'Project ID',
                    'embed' => 'Embed',
                    'event' => 'Event',
                    'created_at' => 'Created At',
                    'updated_at' => 'Updated At',
                    'fullname' => 'Fullname',
                    'email' => 'Email',
                    'elapsed_time' => 'Elapsed Time'
                ];
                break;

            case "survey_report":
                $remove_test_users = 0;
                if ($this->input->get('survey_page_type', true) == "1") {
                    $remove_test_users = 1;
                }
                $data = getSurveyReportAdmin($this->project_id, $start_date, $end_date, $remove_test_users);
                $data = $filterHiddenDomains($data);
                $columns = [
                    "logID" => "Log ID",
                    "Full Name" => "Full Name",
                    "First Name" => "First Name",
                    "Last Name" => "Last Name",
                    "Email" => "Email",
                    "Country" => "Country",
                    "Company" => "Company",
                    "Question 1" => "Question 1",
                    "Question 2" => "Question 2",
                    "Question 3" => "Question 3",
                    "Question 4" => "Question 4",
                    "Question 5" => "Question 5",
                    "Question 6" => "Question 6",
                    "Question 7" => "Question 7",
                    "Question 8" => "Question 8",
                    "Question 9" => "Question 9",
                    "Question 10" => "Question 10",
                    "Question 11" => "Question 11",
                    "Question 12" => "Question 12",
                    "Question 13" => "Question 13",
                    "Question 14" => "Question 14",
                    "Question 15" => "Question 15",
                    "Question 16" => "Question 16",
                    "Question 17" => "Question 17",
                    "Question 18" => "Question 18",
                    "Question 19" => "Question 19",
                    "Question 20" => "Question 20",
                    "Question 21" => "Question 21",
                    "Question 22" => "Question 22",
                    "Question 23" => "Question 23",
                    "Question 24" => "Question 24",
                    "Question 25" => "Question 25",
                    "Question 26" => "Question 26",
                    "Question 27" => "Question 27",
                    "Question 28" => "Question 28",
                    "Question 29" => "Question 29",
                    "Question 30" => "Question 30",
                    "Question 31" => "Question 31",
                    "Question 32" => "Question 32",
                    "Question 33" => "Question 33",
                    "Question 34" => "Question 34",
                    "Question 35" => "Question 35",
                    "Question 36" => "Question 36",
                    "Question 37" => "Question 37",
                    "Question 38" => "Question 38",
                    "Question 39" => "Question 39",
                    "Question 40" => "Question 40",
                    "Type" => "Type",
                    "Tarih" => "Date",
                    "Survey No" => "Survey No"
                ];
                $is_option = true;
                $time_filter = true;
                $select_filter = [
                    "select_filter_name" => 'survey_page_type',
                    "0" => "Pre Survey",
                    "1" => "Post Survey"
                ];
                $select_choose = $this->input->get('survey_page_type', true) ?? "0";
                break;

            case "chat_report":
                $data = getChatReportAdmin($this->project_id, $start_date, $end_date, $remove_test_users);
                $data = $filterHiddenDomains($data);
                $columns = [
                    'logID' => 'logID',
                    'Sender' => 'Sender',
                    'Receiver' => 'Receiver',
                    'Email' => 'Email',
                    'Country' => 'Country',
                    'Company' => 'Company',
                    'Sender Room' => 'Sender Room',
                    'Receiver Room' => 'Receiver Room',
                    'Message' => 'Message',
                    'Message (original)' => 'Message (original)',
                    'Read' => 'Read',
                    'Presenter can see' => 'Presenter can see',
                    'Deleted' => 'Deleted',
                    'Date' => 'Date',
                    'Hidden sender' => 'Hidden sender',
                ];

                $is_option = true;
                $time_filter = true;

                break;

            case "event_report":
                $data = getEventsLogsAdmin($this->project_id);
                $is_option = true;
                $data = $filterHiddenDomains($data);
                $columns = [
                    'fullname' => 'Fullname',
                    'email' => 'Email',
                    'url' => 'Url',
                    'type' => 'Type',
                    'data' => 'Data',
                    'times' => 'Times',
                    'created_at' => 'Created at',
                ];

                break;

            case "polling_report":
                $data = getPollingsAdmin($this->project_id);
                $is_option = true;
                $data = $filterHiddenDomains($data);
                $columns = [
                    'id' => 'ID',
                    'fullname' => 'Fullname',
                    'email' => 'Email',
                    'country' => 'Country',
                    'logid' => 'Log ID',
                    'answer_id' => 'Answer ID',
                    'question_id' => 'Question ID',
                    'question' => 'Question',
                    'answer' => 'Answer',
                    'created_at' => 'Created at',
                ];
                break;

            case "certificate_report":
                $data = getCertificateLogsAdmin($this->project_id);
                $is_option = true;
                $data = $filterHiddenDomains($data);
                $columns = [
                    'id' => 'ID',
                    'log_id' => 'Log ID',
                    'name' => 'Name',
                    'certificate' => 'Certificate',
                    'created_at' => 'Created at',
                    'fullname' => 'Fullname',
                    'email' => 'Email',
                ];

                break;

            case "error_log":
                $data = errorLogAdmin();
                $columns = [
                    'id' => 'ID',
                    'project_id' => 'Project ID',
                    'log_id' => 'Log ID',
                    'error_type' => 'Error Type',
                    'error_content' => 'Error Content',
                    'date_time' => 'Date Time',
                ];

                break;

            case "quiz_report":
                $data = getQuizReport();
                $columns = [
                    'id' => 'Q ID',
                    'project_id' => 'Project ID',
                    'fullname' => 'Fullname',
                    'quiz_no' => 'Quiz No',
                    'embeds' => 'Embeds',
                    'question' => 'Question',
                    'order' => 'Order',
                    'type' => 'Type',
                    'option' => 'Answer',
                    'created_at' => 'Answered Date',
                    'result' => 'Result'
                ];

                break;

            case "tracked_time_report":
                $select_choose = $this->input->get('tracked_page', true);
                $extra_select_choose = $this->input->get('recording_log', true);
                $detailed_columns = $this->input->get('detailed_columns', true) == "true";

                $recordList = trackedTimeRecordList();
                $data = getTrackedTime($start_date, $end_date, $select_choose, $extra_select_choose, $detailed_columns);
                $data = $filterHiddenDomains($data);
                $columns = [
                    'email' => 'Email',
                    'page' => 'Page',
                    $detailed_columns ? 'embed_durations.default' : 'duration'  => 
                        $detailed_columns ? 'Without Embed Duration' : 'Duration',
                ];
                if ($detailed_columns) {
                    $embeds = getEmbedList();
                    foreach ($embeds as $embed) {
                        $columns['embed_durations.' . $embed->lang] = $embed->name . " (" . $embed->lang . ") Duration";
                    }
                }
                $columns['login_date'] = 'Login Date';
                $columns['last_action_date'] = 'Last Action Date';

                $customColumns = $this->input->get('column', true);
                if (!empty($customColumns) && is_array($customColumns)) {
                    foreach ($customColumns as $customColumn => $value) {
                        if (!array_key_exists($customColumn, $columns) && $value == 1) {
                            $columns[$customColumn] = ucwords(implode(' ', explode('_', $customColumn)));
                        }
                    }
                }
                if (!empty($customColumns)) {
                    array_walk($data, function (&$item) use ($customColumns) {
                        $userInf = get_register_data(site_config('project_id'), $item->email);
                        foreach ($customColumns as $k => $v) {
                            $item->{$k} = $userInf->{$k} ?? null;
                        }
                        return true;
                    });
                }

                $is_option = true;
                $time_filter = true;
                $select_filter = [
                    "select_filter_name" => 'tracked_page',
                    "select_filter_title" => 'Select a page'
                ];
                $pages = array_map(function ($item) {
                    return $item->page;
                }, $data);
                $pages = array_unique($pages);
                foreach ($pages as $page) {
                    $select_filter[$page] = ucwords(implode(' ', explode('_', $page)));
                }
                $extra_select_filter = [
                    "extra_select_filter_name" => "recording_log",
                    "extra_select_filter_title" => 'Select a recording log'
                ];

                foreach ($recordList['sessions'] as $sessionRecord) {
                    $extra_select_filter[$sessionRecord['id']] =
                        $sessionRecord['title'] . " (" . $sessionRecord['date'] . ")";
                }
                $columnListByRegister = get_register_columns(site_config('project_id'));
                $records_data_array = $recordList['records_data'];
                break;

            case "login_report":
                $data = getLoginReport($start_date, $end_date, $remove_test_users);
                $data = $filterHiddenDomains($data);
                $columns = [
                    "logID" => "logID",
                    "Full Name" => "Full Name",
                    "First Name" => "First Name",
                    "Last Name" => "Last Name",
                    "Email" => "Email",
                    "Company" => "Company",
                    "Country" => "Country",
                    "Speciality" => "Speciality",
                    "Afilliation" => "Afilliation",
                    "Identity" => "Identity",
                    "GSM" => "GSM",
                    "Page" => "Page",
                    "Number of Audiences" => "Number of Audiences",
                    "Audiences" => "Audiences",
                    "Browser" => "Browser",
                    "Resolution" => "Resolution",
                    "Internet speed" => "Internet speed",
                    "Extra 1" => "Extra 1",
                    "Extra 2" => "Extra 2",
                    "Extra 3" => "Extra 3",
                    "Extra 4" => "Extra 4",
                    "Extra 5" => "Extra 5",
                    "Extra 6" => "Extra 6",
                    "Extra 7" => "Extra 7",
                    "Extra 8" => "Extra 8",
                    "Extra 9" => "Extra 9",
                    "Extra 10" => "Extra 10",
                    "Extra 11" => "Extra 11",
                    "Extra 12" => "Extra 12",
                    "Extra 13" => "Extra 13",
                    "Extra 14" => "Extra 14",
                    "Extra 15" => "Extra 15",
                    "Total Duration (min.)" => "Total Duration (min.)",
                    "IP" => "IP",
                    "Check-in time" => "Check-in time",
                    "Check-out time" => "Check-out time",
                    "Register Time" => "Register Time",
                    "Query String" => "Query String"
                ];

                $is_option = true;
                $time_filter = true;
                break;

            case "register_report":
                $data = getRegisterReport();
                $data = $filterHiddenDomains($data);
                $is_option = true;
                $columns = get_register_columns($this->project_id);
                $columns['create_date'] = "Create Date";
                break;

            case "on_demand_video_report":
                $data = getOnDemandVideoReport();
                $columns = [
                    'fullname' => 'Name',
                    'email' => 'Email',
                    'country' => 'Country',
                    'company' => 'Company',
                    'languagename' => 'Lang',
                    'speciality' => 'Speciality',
                    'c_time' => 'Time',
                    'duration' => 'Duration',
                    'title' => 'Title',
                    'name' => 'Doctor',
                    'finished' => 'Finished',
                    'created_at' => 'Upload Date',
                ];
                break;

            case "all_data_report":
                $this->allDataReportDownloadAdmin();
                break;

            default:
                redirect('/vipanel/v2');
                break;
        }

        switch ($type) {
            case "tracked_time_report":
                $template = 'admin_settings/custom_reports/tracked_time';
                break;
            default:
                $template = 'admin_settings/admin_reports';
                break;
        }

        return $this->template($template, [
            'full_width' => true,
            'breadcrumb' => true,
            'breadcrumb_list' => [
                'Admin Settings',
                'Reports'
            ],
            'breadcrumb_description' => $this->adminNotify,
            'title' => ucwords(implode(' ', explode('_', $type))),
            'columns' => $columns,
            'columnListByRegister' => $columnListByRegister ?? null,
            'data' => $data,
            'records_data' => $records_data_array ?? null,
            'type' => $type,
            'options' => [
                'detailed_columns' => $detailed_columns ?? false,
                'is_option' => $is_option,
                'time_filter' => $time_filter,
                'select_filter' => $select_filter ?? null,
                'select_choose' => $select_choose ?? null,
                'extra_select_filter' => $extra_select_filter ?? null,
                'extra_select_choose' => $extra_select_choose ?? null,
                'hidden_domains' => true,
                'date' => [
                    'start_date' => $start_date,
                    'end_date' => $end_date
                ],
            ]
        ]);
    }

    public function trackedTimeDetail()
    {
        $this->load->model('M_admin_settings', 'm_admin_settings');
        $project_id = $this->session->userdata('site_config')->project_id;
        $email = $this->input->get('email', true);
        $page = $this->input->get('page', true);
        $log = $this->m_admin_settings->getTrackedTimeByRow($project_id, $email, $page);
        return $this->template('admin_settings/custom_reports/tracked_time_detail', [
            'full_width' => true,
            'breadcrumb' => true,
            'breadcrumb_list' => [
                'Admin Settings',
                'Report',
                'Tracked Time Detail'
            ],
            'title' => 'Tracked Time Detail',
            'log' => $log
        ]);
    }

    public function siteUsers()
    {
        $columns = [
            'username' => 'User Name',
            'type' => 'Type'
        ];
        $data = getSiteUsers();
        return $this->template('admin_settings/site_users', [
            'full_width' => true,
            'breadcrumb' => true,
            'breadcrumb_list' => [
                'Admin Settings',
                'Settings'
            ],
            'breadcrumb_description' => $this->adminNotify,
            'title' => 'Site Users',
            'columns' => $columns,
            'data' => $data
        ]);
    }

    public function panelUsers()
    {
        $data = getPanelUsers($this->project_id);
        $columns = [
            'fullname' => 'Fullname',
            'email' => 'Email',
            'country' => 'Country'
        ];


        return $this->template('admin_settings/panel_users', [
            'full_width' => true,
            'breadcrumb' => true,
            'breadcrumb_list' => [
                'Admin Settings',
                'Settings'
            ],
            'breadcrumb_description' => $this->adminNotify,
            'title' => 'Custom Panel Users',
            'columns' => $columns,
            'data' => $data

        ]);
    }

    public function saveAdminSettings()
    {
        $this->isPost();

        if (!empty($_FILES)) {
            $type = $this->input->post('type', true);
            $allData = $this->input->post(null, true);
        } else {
            $type = atm_base64_decode($this->input->post('type', true));
            $allData = $this->parsePostData($this->input->post(null, true));
        }


        $fields = ['web', 'ios', 'android'];
        foreach ($fields as $field) {
            if (isset($allData[$field])) {
                $allData[$field] = $this->input->post($field, false);
            }
        }


        switch ($type) {
            case "create_custom_panel_users":
                if ($allData['email'] && $allData['password']) {
                    $add_data = array(
                        "fullname" => $allData['fullname'],
                        "email" => $allData['email'],
                        "password" => password_hash($allData['password'], PASSWORD_DEFAULT),
                        "country" => implode(',', $allData['country']),
                        "project_id" => $this->project_id
                    );
                    $check = getPanelUserByEmail($this->project_id, $allData['email']);
                    if (empty($check)) {
                        $status = addPanelUser($add_data);
                    } else {
                        $status = false;
                        $message = true;
                    }
                }
                break;

            case "delete_custom_panel_users":
                $status = deletePanelUser($allData['id']);
                break;

            case "edit_custom_panel_users":
                if ($allData['user_id']) {
                    $data = [];
                    if ($_POST) {
                        $fullname = $allData['fullname'];
                        $email = $allData['email'];
                        $password = $allData['password'];
                        $country = $allData['country'];
                        $project_id = $this->session->userdata('site_config')->project_id;
                        if ($fullname && $email && $country) {
                            $update_data = [
                                "fullname" => $fullname,
                                "email" => $email,
                                "country" => implode(',', $country),
                                "project_id" => $project_id
                            ];
                            if (!empty($password)) {
                                $update_data['password'] = password_hash($password, PASSWORD_DEFAULT);
                            }

                            $status = updatePanelUser($update_data, $allData['user_id']);
                        }
                    }
                } else {
                    $status = false;
                }

                break;

            case "create_vipanel_config_user":
                if (($password = $allData['password']) === $allData['re-password']) {
                    $data = [
                        'project_id' => site_config('project_id'),
                        'email' => $allData['email'],
                        'password' => password_hash($password, PASSWORD_DEFAULT),
                        'fullname' => $allData['fullname'],
                        'country' => $allData['country'],
                        'company' => $allData['company'],
                        'permissions' => json_encode($allData['permissions'])
                    ];
                    $status = create_user_admin($data);
                    $message = true;
                } else {
                    $status = false;
                    $message = true;
                }


                break;

            case "edit_vipanel_config_user":
                $user_id = $allData['user_id'];
                $data = [
                    'email' => $allData['email'],
                    'fullname' => $allData['fullname'],
                    'country' => $allData['country'],
                    'company' => $allData['company'],
                    'permissions' => json_encode($allData['permissions'])
                ];
                if (!empty(($password = $allData['password']))) {
                    if ($password === $allData['re-password']) {
                        $data['password'] = password_hash($password, PASSWORD_DEFAULT);
                    } else {
                        $status = false;
                        $message = true;
                    }
                }
                $status = update_user_admin($data, $user_id);
                break;

            case "delete_vipanel_user":
                $status = delete_user_admin($allData['id']);
                break;

            case "create_disclaimer_configs":
                $status = placeholder_disclaimer_update_admin($allData['placeholder_disclaimer_1'], $allData['placeholder_disclaimer_2']);
                break;

            case "registerMapping":
                $filtered_data = !empty($allData['data']) ? $allData['data'] : [];
                $status = update_register_mapping_admin($filtered_data);
                break;

            case "hiddenDomains":
                $filtered_data = [];
                $allData = !empty($allData['hidden_domain_name']) ? $allData['hidden_domain_name'] : [];
                foreach ($allData as $key => $value) {
                    if ($key == "type") {
                        continue;
                    }
                    $filtered_data[] = $value;
                }
                $status = update_hidden_domains_admin($filtered_data);
                break;

            case "create_on_demand_video":
                $status = createOnDemandVideo($allData);
                if ($status) {
                    redirect('/vipanel/v2/admin-settings/on-demand-video');
                    exit;
                }
                break;

            case "edit_on_demand_video":
                $status = editOnDemandVideo($allData);
                if ($status) {
                    redirect('/vipanel/v2/admin-settings/on-demand-video');
                    exit;
                }
                break;

            case "delete_on_demand_video":
                $status = deleteOnDemandVideo($allData['id']);
                break;

            case "create_site_user":
                $status = addSiteUser($allData);
                break;

            case "edit_site_user":
                $status = updateSiteUser($allData);
                break;

            case "delete_site_user":
                $status = deleteSiteUser($allData["id"]);
                break;

            default:
                redirect('/vipanel/v2');
                break;
        }

        return $this->json([
            'status' => $status ?? false,
            'message' => $message ?? false,
        ]);
    }


    public function vipanelConfig()
    {
        $data = [];
        $data['permission_levels'] = $this->permissionLevels;
        $data['user_list'] = get_users_list_admin(site_config('project_id'));

        return $this->template('admin_settings/vipanel_config', [
            'full_width' => true,
            'breadcrumb' => true,
            'breadcrumb_list' => [
                'Admin Settings',
                'Settings'
            ],
            'breadcrumb_description' => $this->adminNotify,
            'title' => 'Vipanel Config',
            'data' => $data
        ]);
    }

    public function recordingTimeLogger()
    {
        $is_active = atm_base64_decode($this->input->post('value'));
        $title = atm_base64_decode($this->input->post('title'));
        $title = $this->security->xss_clean($title);
        try {
            if (($webinar_is_completed = project_config('webinar_is_completed')) !== false) {
                if ($is_active == "0" && $webinar_is_completed === "0") {
                    update_config('webinar_is_completed', 1);
                } elseif ($is_active == "1" && $webinar_is_completed === "1") {
                    update_config('webinar_is_completed', 0);
                }
            }
            $project_id = $this->session->userdata('site_config')->project_id;
            $existData = $this->m_router->get_recording_time_row($project_id);

            if (empty($existData)) {
                $this->m_router->set_recording_time($project_id);
                $is_active = 1;
            } else {
                $this->m_router->update_recording_time($existData->id, $project_id, $title);
                $is_active = 0;
            }
            $status = true;
        } catch (\Exception $e) {
            $status = false;
        }
        return $this->json([
            'status' => $status,
            'is_active' => $is_active
        ]);
    }

    public function afmeUsers()
    {
        $columns = [
            'email' => 'Email',
            'country' => 'Country'
        ];
        $data = [];
        return $this->template('admin_settings/afme_users', [
            'full_width' => true,
            'breadcrumb' => true,
            'breadcrumb_list' => [
                'Admin Settings',
                'Settings'
            ],
            'breadcrumb_description' => $this->adminNotify,
            'title' => 'Afme Users',
            'columns' => $columns,
            'data' => $data
        ]);
    }
    public function afmeUsersAjax(){
        $postData = $postDataAll = $this->input->post();

        if($postDataAll['draw'] != 1) {
            $postData['draw'] = isset($postDataAll['draw']) ? atm_base64_decode($postDataAll['draw']) : 1;
            $postData['start'] = atm_base64_decode($postDataAll['start']);
            $postData['draw'] = isset($postDataAll['draw']) ? atm_base64_decode($postDataAll['draw']) : 1;
            $postData['start'] = isset($postDataAll['start']) ? atm_base64_decode($postDataAll['start']) : 0;
            $postData['length'] = isset($postDataAll['length']) ? atm_base64_decode($postDataAll['length']) : 10; // Rows display per page
            $postData['order'][0]['column'] = isset($postDataAll['order'][0]['column']) ? atm_base64_decode($postDataAll['order'][0]['column']) : 0; // Column index
            $postData['columns'][$postData['order'][0]['column']]['data'] = isset($postDataAll['columns'][$postData['order'][0]['column']]['data']) ? atm_base64_decode($postDataAll['columns'][$postData['order'][0]['column']]['data']) : 'id'; // Column name
            $postData['order'][0]['dir'] = isset($postDataAll['order'][0]['dir']) ? atm_base64_decode($postDataAll['order'][0]['dir']) : 'asc'; // asc or desc
            $postData['search']['value'] = isset($postDataAll['search']['value']) ? atm_base64_decode($postDataAll['search']['value']) : ''; // Search value
        }

        $this->load->model('M_import', 'm_import');
        $result = $this->m_import->getAfmeUsers($postData);

        echo json_encode($result);
        exit();
    }
    function allDataReportDownloadAdmin()
    {
        ini_set('memory_limit', '1256M');

        $this->load->model('M_admin_settings', 'm_admin_settings');
        $this->load->library('excel');

        $project_id = $this->session->userdata('site_config')->project_id;

        $this->data['start_date'] = $this->input->get('start_date', true);
        $this->data['end_date'] = $this->input->get('end_date', true);

        if (!$this->data['start_date']) {
            $this->data['start_date'] = date('Y-m-d', strtotime('-1 year'));
        }

        if (!$this->data['end_date']) {
            $this->data['end_date'] = date('Y-m-d');
        }

        $this->data['remove_test_users'] = null;
        if ($this->input->post('remove_test', true)) {
            $this->data['remove_test_users'] = 1;
        }

        $datas = ['logins', 'registers', 'chats', 'surveys', 'certificate', 'pollings', 'embeds', 'events', 'ondemandvideo', 'tracked_time'];

        $this->data['headers'] = [];
        $this->data['headerss'] = [];
        $this->data['reports'] = $this->m_admin_settings->getAllReportAdmin($project_id, $this->data['start_date'], $this->data['end_date'], $this->data['remove_test_users']);

        $this->data['excel_export'] = 1;

        if (isset($this->data['excel_export'])) {
            $objPHPExcel = new PHPExcel();
            foreach ($datas as $y => $d) {
                if ($y != 0) {
                    $objPHPExcel->createSheet();
                }

                if (isset($this->data['reports']) && isset($this->data['reports'][$d][0]) && is_array($this->data['reports'][$d][0])) {
                    $this->data['headers'] = array_keys($this->data['reports'][$d][0]);
                    $this->data['headerss'] = array_values($this->data['reports']['headers'][$d]);
                    $row = 1;
                    for ($i = 0; $i < count($this->data['headerss']); $i++) {
                        $chr = chr(65 + ($i));
                        if ($i > 25) {
                            $chr = chr(64 + ($i / 26)) . chr(65 + ($i % 26));
                        }
                        $objPHPExcel->setActiveSheetIndex($y)->setCellValue($chr . $row, $this->data['headerss'][$i]);
                    }
                    $row++;

                    $i = 2; //2. satırdan başla ilk satırda header'lar var
                    foreach ($this->data['reports'][$d] as $report) {
                        for ($k = 0; $k < count($report); $k++) {
                            $chr = chr(65 + ($k));
                            if ($k > 25) {
                                $chr = chr(64 + ($k / 26)) . chr(65 + ($k % 26));
                            }
                            $objPHPExcel->setActiveSheetIndex($y)->setCellValue($chr . $i, $report[$this->data['headers'][$k]]);
                        }

                        $i++;
                    }
                    $row++;
                } else {
                    $objPHPExcel->setActiveSheetIndex($y)
                        ->setCellValue('A1', 'No records found.');
                }
                $objPHPExcel->getActiveSheet()->setTitle($d);
            }

            $objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="' . $this->session->userdata('site_config')->url . '_user_report' . date('Y-m-d_H:i:s') . '.xlsx"');
            header('Cache-Control: max-age=0');
            $objWriter->save('php://output');
            die;
        }

        // $this->load->view('report/index', $this->data);
    }
}
