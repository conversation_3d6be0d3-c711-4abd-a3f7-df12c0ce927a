<?php

class Vi_import extends vipanelController
{
    private $projectId;
    private string $import_folder;

    public function __construct()
    {
        parent::__construct();

        $this->load->model('M_import', 'm_import');
		$this->import_folder = ASSETPATH . 'static/' . $this->session->userdata('site_config')->url.'/imports/';
        $this->projectId = site_config('project_id');
        $this->load->helper(array('form', 'url'));
    }

    public function afmeUsersImport()
    {
        ini_set('memory_limit', '8192M');
        ini_set('max_execution_time', 0);

        $response['success'] = FALSE;
        $response['message'] = "Import was failed";

        require_once APPPATH . "/third_party/PHPExcel.php";
        $config['upload_path'] = $this->import_folder;
        $config['allowed_types'] = 'xlsx|xls';
        $config['remove_spaces'] = TRUE;
        $this->load->library('upload', $config);
        $this->upload->initialize($config);
        if (!is_dir($this->import_folder)) {
            mkdir($this->import_folder, 0777);
        }
        if (!$this->upload->do_upload('importFile')) {
            $error = array('error' => $this->upload->display_errors());
        } else {
            $data = array('upload_data' => $this->upload->data());
        }
        if(empty($error)){
            if (!empty($data['upload_data']['file_name'])) {
                $import_xls_file = $data['upload_data']['file_name'];
            } else {
                $import_xls_file = 0;
            }
            $inputFileName = $this->import_folder . $import_xls_file;


            try {
                $inputFileType = PHPExcel_IOFactory::identify($inputFileName);
                $objReader = PHPExcel_IOFactory::createReader($inputFileType);
                $objPHPExcel = $objReader->load($inputFileName);
                $allDataInSheet = $objPHPExcel->getActiveSheet()->toArray(null, true, true, true);
                $flag = true;
                $i=0;
                foreach ($allDataInSheet as $value) {
                    if($flag){
                        $flag =false;
                        continue;
                    }
                    $last_email = "";
                    foreach(['B','C','D'] as $k => $v){
                        if($value[$v] && !empty($value[$v]) && $value[$v] != $last_email){
                            $insertdata[$i]['email'] = $value[$v];
                            $insertdata[$i]['hcp_id'] = $value['A'];
                            $insertdata[$i]['speciality'] = $value['E'];
                            $insertdata[$i]['country'] = $value['F'];
                            $i++;
                            $last_email = $value[$v];
                        }
                    }

                }
                $result = $this->m_import->importAfmeUsers($insertdata);
                if($result){
                    $response['success'] = TRUE;
                    $response['message'] = "Import was successful";
                }else{
                    $response['success'] = FALSE;
                    $response['message'] = "Tmport was failed";
                }

            } catch (Exception $e) {
                die('Error loading file "' . pathinfo($inputFileName, PATHINFO_BASENAME)
                    . '": ' .$e->getMessage());
            }
        }

        $this->session->set_flashdata('import_alert', $response);
        redirect('/vipanel/v2/admin-settings/afme-users');
    }

}
