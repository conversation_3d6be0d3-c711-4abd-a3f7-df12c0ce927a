<?php

class Vi_transactional_emails extends vipanelController
{
	private SendGridService $sendGridService;
	private bool $isAdmin;
	public ?string $dateTimeFormat = 'd M Y H:i - \G\M\T P';

	public function __construct()
	{
		parent::__construct();
		$this->load->helper('sendgrid_helper');
		$this->load->model('M_transactional_emails', 'm_transactional_emails');
		$this->sendGridService = new SendGridService();
		$this->isAdmin = $this->session->site_vipanel['is_admin'] ?? false;
	}

	public function index()
	{
		$sg = new SendGridService();

		$allSends = $sg->getSingleSends();
		$lastUpdate = $sg->getLastUpdate('single_sends');
		$updateMessage = $lastUpdate ?
			'Last update: ' . edit_timezone(
				date('Y-m-d H:i:s', $lastUpdate),
				project_config_default('site_timezone', 'Europe/Istanbul')
			)->format($this->dateTimeFormat)
			: 'No data';

		$this->template('transactional_emails/index', [
			'breadcrumb' => true,
			'breadcrumb_description' => 'This page is for managing transactional emails.<br>' . $updateMessage,
			'breadcrumb_image' => true,
			'full_width' => true,
			'allSends' => $allSends,
			'dateTimeFormat' => $this->dateTimeFormat,
		]);
	}

	public function statistic($id)
	{
		$sg = new SendGridService();
		$detail = $sg->getSingleSendById($id);
		$sync = $this->input->get('sync', true) == '1';
		if ($sync) {
			$sg->cacheDelete('single_send_stats', $id ?? "");
			$sg->cacheDelete('single_send_link_stats', $id ?? "");
			return $this->json([
				'status' => true
			]);
		}

		$stats = $sg->getSingleSendStats($id ?? "");
		$stats = reset($stats);

		$link_stats = $sg->getSingleSendLinkStats($id);

		$this->template('transactional_emails/statistic', [
			'title' => $detail->name . ' - Statistics',
			'breadcrumb' => true,
			'breadcrumb_list' => [
				'Transactional Emails'
			],
			'breadcrumb_button' => [
				'path' => '/vipanel/v2/transactional-emails',
				'title' => 'Back to Transactional Emails',
				'type' => 'info'
			],
			'breadcrumb_image' => true,
			'full_width' => true,
			'id' => $id,
			'stat' => $stats,
			'detail' => $detail,
			'link_stats' => $link_stats,
			'last_update' => [
				'stat' => $sg->getLastUpdate('single_send_stats', $id),
				'link_stats' => $sg->getLastUpdate('single_send_link_stats', $id),
			],
			'dateTimeFormat' => $this->dateTimeFormat
		]);
	}

	public function edit($id)
	{
		if (!$this->isAdmin) {
			redirect('/vipanel/v2/transactional-emails');
		}
		$sg = new SendGridService();
		$senders = $sg->getSenders();
		$lists = $sg->getLists();
		$detail = $sg->getSingleSendById($id, false);

		$templates = get_pages(site_config('project_id'));
		$templates = array_filter($templates, function ($template) {
			return strpos($template->url, 'mail');
		});
		usort($templates, function ($a, $b) {
			return strcmp($a->name, $b->name);
		});

        $transactionalTemplates = $this->getTemplateTransactionalEmails();

		$this->template('transactional_emails/edit', [
			'title' => $detail->name,
			'breadcrumb' => true,
			'breadcrumb_list' => [
				'Transactional Emails'
			],
			'breadcrumb_button' => [
				'path' => '/vipanel/v2/transactional-emails',
				'title' => 'Back to Transactional Emails',
				'type' => 'info'
			],
			'breadcrumb_image' => true,
			'full_width' => true,
			'id' => $id,
			'detail' => $detail,
			'senders' => $senders,
			'lists' => $lists,
			'templates' => $templates,
            'transactionalTemplates' => $transactionalTemplates,
			'last_update' => [
				'senders' => $sg->getLastUpdate('single_send_senders'),
				'list' => $sg->getLastUpdate('single_send_contacts'),
			],
			'dateTimeFormat' => $this->dateTimeFormat,
		]);
	}

	public function contacts()
	{
		if (!$this->isAdmin) {
			redirect('/vipanel/v2/transactional-emails');
		}
		$sg = new SendGridService();

		$contacts = $sg->getLists();
		$lastUpdate = $sg->getLastUpdate('single_send_contacts');
		$contactLimits = $sg->getContactLimits();

		$updateMessage = $lastUpdate ?
		'Last update: ' . edit_timezone(
			date('Y-m-d H:i:s', $lastUpdate),
			project_config_default('site_timezone', 'Europe/Istanbul')
			)->format($this->dateTimeFormat)
			: 'No data';

		$updateMessage = implode(' | ', [
			$updateMessage,
			'Contact Limits: ' . $contactLimits['contact_count'] . ' / ?'
		]);

		$this->template('transactional_emails/contacts', [
			'title' => 'Manage Contacts',
			'breadcrumb' => true,
			'breadcrumb_list' => [
				'Transactional Emails',
			],
			'breadcrumb_button' => [
				'path' => '/vipanel/v2/transactional-emails',
				'title' => 'Back to Transactional Emails',
				'type' => 'info'
			],
			'breadcrumb_image' => true,
			'breadcrumb_description' => $updateMessage,
			'contacts' => $contacts,
			'contact_limits' => $contactLimits
		]);
	}

	public function contactManagement($id)
	{
		if (!$this->isAdmin) {
			redirect('/vipanel/v2/transactional-emails');
		}
		$sg = new SendGridService();
		$isAdd = $this->uri->segment(6) == 'add';
		$detail = $sg->getListById($id ?? "", false);
		$this->template(
			$isAdd ? 'transactional_emails/contact_add' : 'transactional_emails/contact_remove', [
			'title' => $isAdd ? $detail['name'] . ' - Add Contacts' : $detail['name'] . ' - Remove Contacts',
			'breadcrumb' => true,
			'breadcrumb_list' => [
				'Transactional Emails',
				'Manage Contacts'
			],
			'breadcrumb_button' => [
				'path' => '/vipanel/v2/transactional-emails/contacts',
				'title' => 'Back to Contacts',
				'type' => 'info'
			],
			'breadcrumb_image' => true,
			'id' => $id,
			'detail' => $detail,
			'dateTimeFormat' => $this->dateTimeFormat,
		]);
	}

	public function contactPreview($id)
	{
		if (!$this->isAdmin) {
			redirect('/vipanel/v2/transactional-emails');
		}
		$sg = new SendGridService();
		$detail = $sg->getSampleContactList($id ?? "");
		$detail['name'] = explode('|', $detail['name']);
		$this->template('transactional_emails/contact_preview', [
			'title' => 'Preview Contacts',
			'breadcrumb' => true,
			'breadcrumb_list' => [
				'Transactional Emails',
				'Manage Contacts'
			],
			'breadcrumb_description' =>
				'Showing ' . count($detail['contact_sample']) . '/' . $detail['contact_count'] . ' contacts',
			'breadcrumb_button' => [
				'path' => '/vipanel/v2/transactional-emails/contacts',
				'title' => 'Back to Contacts',
				'type' => 'info'
			],
			'breadcrumb_image' => true,
			'id' => $id,
			'detail' => $detail,
			'dateTimeFormat' => $this->dateTimeFormat,
		]);
	}

	public function unsubscribe()
	{
		if (!$this->isAdmin) {
			redirect('/vipanel/v2/transactional-emails');
		}
		$sg = new SendGridService();

		$contacts = $sg->getUnsubscribeList();
		$this->template('transactional_emails/unsubscribe', [
			'title' => 'Unsubscribe List',
			'breadcrumb' => true,
			'breadcrumb_list' => [
				'Transactional Emails',
			],
			'breadcrumb_button' => [
				'path' => '/vipanel/v2/transactional-emails',
				'title' => 'Back to Transactional Emails',
				'type' => 'info'
			],
			'breadcrumb_image' => true,
			'contacts' => $contacts,
			'dateTimeFormat' => $this->dateTimeFormat,
		]);
	}

	public function make($id)
	{
		if (!$this->isAdmin) {
			redirect('/vipanel/v2/transactional-emails');
		}
		$data = [];
		if (!empty($this->input->post('data'))) {
			$data = $this->input->post('data');
			if (is_array($data)) {
				$data = $this->parsePostData($data);
			}
		}
		switch ($id) {
			case "create_contact":
				$status = $this->sendGridService->createList($data['name'] ?? "");
				$result = [
					'status' => $status,
				];
				break;
			case "delete_contact":
				$status = $this->sendGridService->deleteList($data['id'] ?? "");
				$result = [
					'status' => $status,
				];
				break;
			case "export_contacts":
				$status = $this->sendGridService->exportContacts($this->input->get('id'));
				$result = [
					'status' => $status,
				];
				break;
			case "add_contacts":
				$data = $this->input->post('data');
				$data = atm_base64_decode(atm_base64_decode($data));
				$data = json_decode($data, true);
				$status = $this->sendGridService->addContacts(
					atm_base64_decode($this->input->post('id')),
					$data,
					atm_base64_decode($this->input->post('form_name'))
				);
				$result = [
					'status' => $status,
				];
				break;
			case "remove_contacts":
				$statuses = [];
				foreach ($data as $item) {
					$statuses[] = $this->sendGridService->removeContacts(
						$item['contact_id'], $item['email'], atm_base64_decode($this->input->post('id'))
					);
				}
				$result = [
					'status' => !in_array(false, $statuses),
				];
				break;
			case "create_email":
				$status = $this->sendGridService->createSingleSend($this->input->get('name'));
				$result = [
					'status' => $status
				];
				break;
			case "update_email":
				$data = $this->input->post(null, true);
				$data = $this->parsePostData($data, false);
				$status = $this->sendGridService->updateSingleSend($data['id'], $data);
				$result = [
					'status' => $status
				];
				break;
			case "publish_email":
				$result = $this->sendGridService->publishSingleSend(atm_base64_decode($this->input->post('id')));
				break;
			case "cancel_schedule":
				$status = $this->sendGridService->cancelScheduledSingleSend(atm_base64_decode($this->input->post('id')));
				$result = [
					'status' => $status
				];
				break;
			case "delete_single_send":
				$status = $this->sendGridService->deleteSingleSend(atm_base64_decode($this->input->post('id')));
				$result = [
					'status' => $status
				];
				break;
			case "send_test_email":
				$data = $this->input->post(null, true);
				$data = $this->parsePostData($data);
				$status = $this->sendGridService->sendTestEmail(
					$data['id'], $data['emails']
				);
				$result = [
					'status' => $status
				];
				break;
			case "retrieve_register":
				$result = $this->sendGridService->retrieveRegister(
					$this->input->get('form_name')
				);
				$result = array_map(function ($item) {
					$data = json_decode($item->data, true);
					$fullName = explode(' ', $data['fullname'] ?? "");
					if (empty($data['first_name'])) {
						$data['first_name'] = count($fullName) > 0 ? reset($fullName) : "";
					}
					if (empty($data['last_name'])) {
						$data['last_name'] = count($fullName) > 1 ? end($fullName) : "";
					}
					return [
						'email' => $data['email'] ?? "-",
						'first_name' => $data['first_name'] ?? "-",
						'last_name' => $data['last_name'] ?? "-"
					];
				}, $result);
				break;
			case "retrieve_template":
				$result = $this->sendGridService->retrieveTemplate(
					$this->input->get('template')
				);
				break;
			case "save_template":
				$data = $this->input->post(null, true);
				$data = $this->parsePostData($data);
				$status = $this->sendGridService->saveTemplate($data['id'], $data['content']);
				$result = [
					'status' => $status
				];
				break;
			case "retrieve_senders":
				$q = $this->input->get('q', true);
				$data = $this->sendGridService->getSenders(false, $q);
				$data = array_map(function ($item) {
					return [
						'id' => $item->id,
						'text' => $item->from['name'] . ' <' . $item->from['email'] . '>'
					];
				}, $data);
				if (empty($data)) {
					$data = [
						[
							'id' => -1,
							'text' => 'Create a sender as "' . $q . '"'
						]
					];
				}
				$result = [
					'items' => $data
				];
				break;
			case "create_sender":
				$data = $this->input->post(null, true);
				$data = $this->parsePostData($data);
				$status = $this->sendGridService->createSender($data['name'], $data['email'], $data['reply_to']);
				$result = [
					'status' => $status
				];
				break;
			case "retrieve_magical_list":
				$result = $this->sendGridService->retrieveMagicalList(
					atm_base64_decode($this->input->post('type', true)),
					atm_base64_decode($this->input->post('form_name', true)) ?? null
				);
				break;
			case "update_form_name":
				$status = $this->sendGridService->updateListForm(
					atm_base64_decode($this->input->post('id', true)),
					atm_base64_decode($this->input->post('form_name', true))
				);
				$result = [
					'status' => $status
				];
				break;
			case "search_contact":
				$result = $this->sendGridService->searchContact(
					atm_base64_decode($this->input->post('email', true)),
					atm_base64_decode($this->input->post('list_id', true))
				);
				break;
			case "refresh_contact":
				$result = $this->sendGridService->getListById(
					$this->input->get('id', true)
				);
				break;
			default:
				$result = [];
				break;
		}
		return $this->json($result);
	}

	public function check($id)
	{
		if (!$this->isAdmin) {
			redirect('/vipanel/v2/transactional-emails');
		}
		switch ($id) {
			case 'check_exports':
				$result = [
					'count' => $this->sendGridService->getExportingCount(),
				];
				break;
			case 'check_import':
				$result = [
					'count' => $this->sendGridService->getImportingCount($this->input->get('id')),
				];
				break;
			case 'check_remove':
				$result = [
					'count' => $this->sendGridService->getRemovingCount($this->input->get('id')),
				];
				break;
			default:
				$result = [];
				break;
		}
		return $this->json($result);
	}

    public function getTemplateTransactionalEmails()
    {
        $CI = get_instance();
        $CI->db->from('template_transactional_emails')
            ->order_by('title', 'ASC');

        return $CI->db->get()->result();
    }

    public function ajaxEmailTemplateHtml()
    {
        if (!$this->isAdmin) {
            redirect('/vipanel/v2/transactional-emails');
        }

        $id = atm_base64_decode($this->input->post('id'));

        $CI = get_instance();
        $CI->db->from('template_transactional_emails')
            ->where('id', $id);

        $template = $CI->db->get()->row();

        return $this->json([
            'status' => true,
            'html' => $template->content
        ]);
    }
}