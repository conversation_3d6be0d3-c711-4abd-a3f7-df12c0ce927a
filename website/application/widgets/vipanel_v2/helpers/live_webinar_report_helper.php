<?php

function getReactionReport($startDate = null, $endDate = null)
{
    $filter = [
        'url' => site_config('url')
    ];
    if (!empty($startDate) && !empty($endDate)) {
        $filter['timestamp'] = [
            '$gte' => new MongoDB\BSON\UTCDateTime(strtotime($startDate) * 1000),
            '$lte' => new MongoDB\BSON\UTCDateTime(strtotime($endDate) * 1000)
        ];
    }
    $data = get_mongodb_data('reactions', $filter);
    $result = [];
    foreach ($data as $item) {
        if (!empty($result[$item['email']][$item['emoji']][$item['room']])) {
            $result[$item['email']][$item['emoji']][$item['room']] += 1;
        } else {
            $result[$item['email']][$item['emoji']][$item['room']] = 1;
        }
    }
    return $result;
}

function getLiveWebinarLoginReport($startDate = null, $endDate = null, $remove_test_users = null){
    $start_date = date('Y-m-d H:i:s', strtotime($startDate . ' 00:00:00'));
    $end_date = date('Y-m-d H:i:s', strtotime($endDate . ' 23:59:59'));

    $project_id = site_config('project_id');

    $CI = &get_instance();
    $q = $CI->db->query('CALL getSiteReport(?,?,?,?)', array($project_id, $start_date, $end_date, $remove_test_users));
    if ($q !== false) {
        return $q->result_array();
    }
}
