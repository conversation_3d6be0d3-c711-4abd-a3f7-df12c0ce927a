<?php

class Vi_vipanel extends adminController
{
	protected $permissionLevels = [
		'dashboard' => [],
		'livestreaming_setup' => [
			'stream_embed',
			'rtmp_url',
			'breakout_rooms',
			'backstage_setup',
			'showflow_planning'
		],
		'registration_management' => [
			'registration_list',
			'register_management',
			'p_reg_email_settings',
			'access_restrictions',
			'login_settings',
			'timezone_settings'
		],
		'interactive_tools' => [
			'qa_modules',
			'polling_modules',
			'pre_survey_setup',
			'post_survey_setup',
			'certificate_modules',
			'quiz_modules',
			'notifications',
			'announcements',
			'topic_management'
		],
		'live_webinar_reports' => [
			'attendee_report',
			'login_report',
			'chat_report',
			'polling_report',
			'survey_report',
			'reaction_report',
			'webinar_metrics_insights'
		],
    'ondemand_video' => [],
		'transactional_emails' => [
			'email_templates'
		],
	];

	public function __construct()
	{
		parent::__construct();
	}

	public function index()
	{
		$this->data['user_list'] = $this->db->from('vipanel_users')->where('project_id', site_config('project_id'))->get()->result();
		$this->load->view("settings/vipanel/index", $this->data);
	}

	public function config()
	{
		$this->load->view("settings/vipanel/config", $this->data);
	}

	public function configSave()
	{
		$this->isPost();
		$this->load->helper('vipanel_helper');

		switch ($this->input->post('type', true)) {
			case "disclaimer":
				$status = placeholderDisclaimerUpdate(
					$this->input->post('placeholder_1', true),
					$this->input->post('placeholder_2', true)
				);
				break;
			case "registerMapping":
				$status = updateRegisterMapping(
					$this->input->post('data', true)
				);
				break;
			case "hiddenDomains":
				$status = updateHiddenDomains(
					$this->input->post('data', true)
				);
				break;
			default:
				$status = false;
				break;
		}
		$this->json([
			'status' => $status
		]);
		exit;
	}

	public function createUser()
	{
		if ($this->input->server('REQUEST_METHOD') === 'POST') {
			if (($password = $this->input->post('password', true)) === $this->input->post('re-password', true)) {
				$data = [
					'project_id' => site_config('project_id'),
					'email' => $this->input->post('email', true),
					'password' => password_hash($password, PASSWORD_DEFAULT),
					'fullname' => $this->input->post('fullname', true),
					'country' => $this->input->post('country', true),
					'company' => $this->input->post('company', true),
					'permissions' => json_encode($this->input->post('permissions', true))
				];
				$this->db->insert('vipanel_users', $data);
				$this->session->set_flashdata('user_action', 'The user has been successfully created.');
				return redirect('vistreampanel/v2/settings/vipanel');
			} else {
				$this->session->set_flashdata('re_password_error', 'Please enter the same passwords.');
				return redirect('vistreampanel/v2/settings/vipanel/create-user');
			}
		}
		$this->data['permission_levels'] = $this->permissionLevels;
		$this->load->view("settings/vipanel/createUser", $this->data);
	}

	public function deleteUser()
	{
		if (!empty($id = $this->input->get('id', true))) {
			$this->db->delete('vipanel_users', array('id' => $id));
		}
		$this->session->set_flashdata('user_action', 'The user has been successfully deleted.');
		return redirect('vistreampanel/v2/settings/vipanel');
	}

	public function editUser()
	{
		if (empty($id = $this->input->get('id', true))) {
			return redirect('vistreampanel/v2/settings/vipanel');
		}
		if ($this->input->server('REQUEST_METHOD') === 'POST') {
			$user_id = $this->input->post('user_id', true);
			$data = [
				'email' => $this->input->post('email', true),
				'fullname' => $this->input->post('fullname', true),
				'country' => $this->input->post('country', true),
				'company' => $this->input->post('company', true),
				'permissions' => json_encode($this->input->post('permissions', true))
			];
			if (!empty(($password = $this->input->post('password', true)))) {
				if ($password === $this->input->post('re-password', true)) {
					$data['password'] = password_hash($password, PASSWORD_DEFAULT);
				} else {
					$this->session->set_flashdata('re_password_error', 'Please enter the same passwords.');
					return redirect('vistreampanel/v2/settings/vipanel/edit-user?id=' . $user_id);
				}
			}
			$this->db->set($data)->where('id', $user_id)->update('vipanel_users');
			$this->session->set_flashdata('user_edit_success', 'You have successfully edited the user.');
			return redirect('vistreampanel/v2/settings/vipanel/edit-user?id=' . $user_id);
		}
		$this->data['edit_user'] = $this->db->from('vipanel_users')->where('id', $id)->get()->row();
		$this->data['permission_levels'] = $this->permissionLevels;
		$this->load->view("settings/vipanel/editUser", $this->data);
	}
}
