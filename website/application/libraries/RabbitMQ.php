<?php
defined('BASEPATH') OR exit('No direct script access allowed');

use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Message\AMQPMessage;
use PhpAmqpLib\Wire\AMQPTable;

/**
 * CodeIgniter RabbitMQ Library
 *
 * A library to interact with RabbitMQ message broker
 *
 * @package     CodeIgniter
 * @subpackage  Libraries
 * @category    Libraries
 * <AUTHOR>
 */
class RabbitMQ {

    /**
     * CodeIgniter instance
     *
     * @var object
     */
    protected $CI;

    /**
     * RabbitMQ connection
     *
     * @var AMQPStreamConnection
     */
    protected $connection;

    /**
     * RabbitMQ channel
     *
     * @var \PhpAmqpLib\Channel\AMQPChannel
     */
    protected $channel;

    /**
     * RabbitMQ configuration
     *
     * @var array
     */
    protected $config;

    /**
     * Constructor
     *
     * @return void
     */
    public function __construct()
    {
        $this->CI =& get_instance();
        $this->CI->config->load('rabbitmq', TRUE);
        $this->config = $this->CI->config->item('rabbitmq', 'rabbitmq');
        
        log_message('info', 'RabbitMQ Class Initialized');
    }

    /**
     * Connect to RabbitMQ server
     *
     * @return bool
     */
    public function connect()
    {
        try {
            $this->connection = new AMQPStreamConnection(
                $this->config['host'],
                $this->config['port'],
                $this->config['user'],
                $this->config['password'],
                $this->config['vhost']
            );
            
            $this->channel = $this->connection->channel();
            
            // Declare the exchange
            $exchange_config = $this->CI->config->item('exchange', 'rabbitmq');
            $this->channel->exchange_declare(
                $exchange_config['name'],
                $exchange_config['type'],
                $exchange_config['passive'],
                $exchange_config['durable'],
                $exchange_config['auto_delete']
            );
            
            log_message('info', 'Connected to RabbitMQ server');
            return true;
        } catch (Exception $e) {
            log_message('error', 'Failed to connect to RabbitMQ server: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Declare a queue
     *
     * @param string $queue_name
     * @param array $args Additional queue arguments
     * @return bool
     */
    public function declare_queue($queue_name, $args = [])
    {
        if (!$this->channel) {
            if (!$this->connect()) {
                return false;
            }
        }
        
        try {
            $queue_config = $this->CI->config->item('queue', 'rabbitmq');
            
            $queue_args = null;
            if (!empty($args)) {
                $queue_args = new AMQPTable($args);
            }
            
            $this->channel->queue_declare(
                $queue_name,
                $queue_config['passive'],
                $queue_config['durable'],
                $queue_config['exclusive'],
                $queue_config['auto_delete'],
                false,
                $queue_args
            );
            
            // Bind the queue to the exchange
            $exchange_config = $this->CI->config->item('exchange', 'rabbitmq');
            $this->channel->queue_bind($queue_name, $exchange_config['name'], $queue_name);
            
            log_message('info', 'Queue declared: ' . $queue_name);
            return true;
        } catch (Exception $e) {
            log_message('error', 'Failed to declare queue: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Publish a message to a queue
     *
     * @param string $queue_name
     * @param mixed $message
     * @param array $properties
     * @return bool
     */
    public function publish($queue_name, $message, $properties = [])
    {
        if (!$this->channel) {
            if (!$this->connect()) {
                return false;
            }
        }
        
        try {
            // Ensure the queue exists
            $this->declare_queue($queue_name);
            
            // Prepare message
            $msg_body = is_array($message) || is_object($message) ? json_encode($message) : $message;
            
            // Default properties
            $default_properties = [
                'content_type' => 'application/json',
                'delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT
            ];
            
            // Merge with custom properties
            $msg_properties = array_merge($default_properties, $properties);
            
            $msg = new AMQPMessage($msg_body, $msg_properties);
            
            // Publish the message
            $exchange_config = $this->CI->config->item('exchange', 'rabbitmq');
            $this->channel->basic_publish($msg, $exchange_config['name'], $queue_name);
            
            log_message('info', 'Message published to queue: ' . $queue_name);
            return true;
        } catch (Exception $e) {
            log_message('error', 'Failed to publish message: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Consume messages from a queue
     *
     * @param string $queue_name
     * @param callable $callback
     * @param bool $no_ack
     * @return bool
     */
    public function consume($queue_name, $callback, $no_ack = false)
    {
        if (!$this->channel) {
            if (!$this->connect()) {
                return false;
            }
        }
        
        try {
            // Ensure the queue exists
            $this->declare_queue($queue_name);
            
            // Set quality of service
            $this->channel->basic_qos(null, 1, null);
            
            // Start consuming
            $this->channel->basic_consume(
                $queue_name,
                '',
                false,
                $no_ack,
                false,
                false,
                $callback
            );
            
            log_message('info', 'Started consuming from queue: ' . $queue_name);
            
            // Keep consuming until channel is closed
            while ($this->channel && $this->channel->is_consuming()) {
                $this->channel->wait();
            }
            
            return true;
        } catch (Exception $e) {
            log_message('error', 'Failed to consume messages: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Close the connection
     *
     * @return void
     */
    public function close()
    {
        if ($this->channel) {
            $this->channel->close();
        }
        
        if ($this->connection) {
            $this->connection->close();
        }
        
        log_message('info', 'RabbitMQ connection closed');
    }

    /**
     * Destructor
     *
     * @return void
     */
    public function __destruct()
    {
        $this->close();
    }
}
