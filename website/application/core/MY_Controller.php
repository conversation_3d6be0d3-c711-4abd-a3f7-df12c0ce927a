<?php

if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

class MY_Controller extends CI_Controller
{
    var $data = array(
        'setLayout' => '',
        'safe_mode' => false
    );

    //view'lardan layout kapatılmak istenirse kullanılıyor.
    var $layout = true;

    function __construct()
    {
        parent::__construct();
    }

    function check_logged($session_name, $redirect)
    {
        if (!$this->session->has_userdata($session_name)) {
            redirect($redirect);
        }
    }

    function check_role($session_name, $permission, $redirect)
    {
        if (!$this->session->has_userdata($session_name)) {
            redirect($redirect);
        } else {
            if (isset($this->session->userdata($session_name)->role) && $this->session->userdata($session_name)->role == $permission) {
                redirect($redirect);
            }
        }
    }

    function _output($content)
    {
        if ($this->data['setLayout'] == '' || $this->layout === false) {
            echo $content;
        } else {
            $data['content_for_layout'] = $content;
            echo $this->load->view('layouts/' . $this->data['setLayout'], $data, true);
        }
    }
}

class frontendController extends MY_Controller
{
	function __construct()
	{
		parent::__construct();

		if ($this->session->has_userdata('site_config')) {
			$site_updated = $this->m_router->checkUpdate($this->session->userdata('site_config')->project_id, $this->session->userdata('site_config')->updated_date);
			##if site updated unset config session
			if (!$site_updated || $site_updated->updated == 1) {
				$this->session->unset_userdata('site_config');
			}
		}

		##set default config
		$this->set_default_config();
	}

	private function set_default_config()
	{
		/*
		* default config
		*/
        $uri = $this->uri->uri_string();

		if (!$this->session->has_userdata('site_config')) {

			$site_url = str_replace('www.', '', $this->input->server('HTTP_HOST'));

			$config = $this->m_router->get_project($site_url);
			if ($this->m_router->get_page($config->project_id, $this->uri->uri_string()) !== null) {
				$new_version = $this->m_router->get_page($config->project_id, $this->uri->uri_string())->new_version;
				if ($new_version) {
					$config->layout = 'new_general';
				}
			}

			if (!$config) {
				redirect('/error?code=99');
			}

			$config->stream_lang = false;

			##set site config session
			$this->session->set_userdata('site_config', $config);

			if (!$config->enter_page) {
				redirect('/error?code=101');
			}

			##google analytics
			$this->session->userdata('site_config')->google_analytics = $this->m_router->get_config($config->project_id, 'google_analytics');
			$this->session->userdata('site_config')->footer_content = $this->m_router->get_config($config->project_id, 'footer_content');
			$this->session->userdata('site_config')->xframe = $this->m_router->get_config($config->project_id, 'xframe');
			$this->session->userdata('site_config')->parsed_footer_content = null;
			$this->session->userdata('site_config')->current_lang = null;
			$this->session->userdata('site_config')->site_languages = $this->m_lang->get_project_langs($config->project_id);
		} else {
			$config = $this->session->userdata('site_config');
			if ($this->m_router->get_page($config->project_id, $this->uri->uri_string()) !== null) {
				$page = $this->m_router->get_page($config->project_id, $this->uri->uri_string());
				if (isset($page->new_version)) {
					$new_version = $page->new_version;
					$config->layout = 'new_general';
				}
			}
		}

        //if SSL required
        if ($this->session->userdata('site_config')->ssl_required) {
            if ($this->input->server('HTTPS') === null && $this->input->server('HTTP_X_SCHEME') !== "https" && $this->input->server('HTTP_X_FORWARDED_PROTO') !== "https") {
                $new_url = 'https://' . $this->input->server('HTTP_HOST') . '/' . $this->uri->uri_string();
                redirect($new_url, 'Location', 302);
            }
        }

		##X-Frame-Options
		$xframe_options = $this->session->userdata('site_config')->xframe;
        if (strpos($uri, 'vipanel') !== 0){
            if ($xframe_options) {
                if ($xframe_options == "*") {
                    $this->output->set_header('X-Frame-Options: ALLOWALL');
                } else {
                    $this->output->set_header('X-Frame-Options: ALLOW-FROM ' . $xframe_options);
                }
            } else {
                $this->output->set_header('X-Frame-Options: SAMEORIGIN');
            }
        }

		##set OR read site_lang cookie
		$this->set_lang();

		## Set layout for site ##
		$this->data['setLayout'] = $this->session->userdata('site_config')->layout;
	}

	private function set_lang()
	{
		##select & set site language
		$site_lang = $this->input->cookie('site_lang', true);

		$site_lang_wrong = !$site_lang || strlen($site_lang) > 3 || !is_numeric($site_lang);
		$language_id_allowed = $this->m_lang->project_lang_exist($this->session->userdata('site_config')->project_id, $site_lang);

		##if no site_lang cookie OR site_lang value not allowed for this project
		if ($site_lang_wrong || !$language_id_allowed) {
			##get lang id (IP based)
			$ip_based_lang = $this->m_lang->project_ip_lang(
				$this->session->userdata('site_config')->project_id,
				$this->input->ip_address()
			);

			##set
			if (!empty($ip_based_lang)) {
				$site_lang = $ip_based_lang->site_lang;
			} else {
				$site_lang = $this->session->userdata('site_config')->default_lang;
			}

			##set cookie
			$this->input->set_cookie([
				'name' => 'site_lang',
				'expire' => 86500 * 30,
				'value' => $site_lang,
				"secure" => true,
				"httponly" => true
			]);
		}

		##set current lang to session
		$this->session->userdata('site_config')->current_lang = $site_lang;

		##load lang file
		$this->lang->load('general', $site_lang == 1 ? 'tr' : 'en');
	}

	public function json($data, $status = 200)
	{
		http_response_code($status);
		header('Content-Type: application/json');
		echo json_encode($data);
		return true;
	}

	public function isPost()
	{
		if ($this->input->server('REQUEST_METHOD') !== 'POST') {
			die('Use post request');
		}
	}
  public function parsePostData($data, $xssClean = true)
    {
        $result = [];
        foreach ($data as $key => $item) {
            if (is_string($item)) {
                $result[$key] = !empty($item) ? atm_base64_decode($item) : "";
            } elseif (is_array($item)) {
                foreach ($item as $k => $i) {
                    if (is_string($i)) {
                        $result[$key][$k] = !empty($i) ? atm_base64_decode($i) : "";
                    } elseif (is_array($i)) {
                        foreach ($i as $kk => $ii) {
                            $result[$key][$k][$kk] = !empty($ii) ? atm_base64_decode($ii) : "";
                        }
                    }
                }
            }
        }
        if ($xssClean) {
            $result = $this->security->xss_clean($result);
        }
        return $result;
    }
}

class managerController extends MY_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->check_logged('manager', '/manager/login');
    }
}

class ajaxController extends MY_Controller
{
    var $response = array();

    function __construct()
    {
        parent::__construct();
        $this->data['setLayout'] = ''; //unset layout

        if (!$this->input->is_ajax_request()) {
            redirect('/');
        } //ajax değilse anasayfaya yönlendir.

        $this->response['success'] = false;
        $this->response['message'] = '';

        ##get site lang
        $site_lang = $this->input->cookie('site_lang', true);

        ##load lang file
        $this->lang->load('general', $site_lang == 1 ? 'tr' : 'en');
    }

	public function ReturnResponse(){
		header('Content-Type: application/json');
		echo json_encode($this->response);
        return true;
	}
}

class adminController extends frontendController
{
    function __construct()
    {
        parent::__construct();
        $this->check_logged('site_admin', '/vistreampanel/v2/login');
        $this->v2Check();
        $this->data['setLayout'] = 'general';

        if (
            (
                $this->uri->segment(2) != "report" || $this->uri->segment(3) != "user"
            ) and
            (
                $this->uri->segment(3) != "event-definitions" and
                !(
                    $this->uri->segment(3) == "reports" and
                    $this->uri->segment(4) == "user"
                ) and
                !(
                    $this->uri->segment(4) == "get-data" and
                    $this->uri->segment(5) == "user"
                )
            )
        ) {
            $this->check_role('site_admin', 'reporter', '/vistreampanel/v2/reports/user');
        }
    }

    public function v2Check()
    {
        $uri = explode('/', $this->uri->uri_string());
        if (!in_array('v2', $uri)) {
            $uri[0] = 'vistreampanel/v2';
            redirect('/' . implode('/', $uri));
        }
        return true;
    }
}

class vipanelController extends frontendController
{
    public $sidebar = [
        [
            'key' => 'dashboard',
            'title' => 'Dashboard',
            'icon' => 'ti ti-archive',
            'path' => '/vipanel/v2',
        ], [
            'key' => 'on_demand_video',
            'title' => 'On Demand Video',
            'icon' => 'ti ti-video',
            'path' => '/vipanel/v2/on-demand-video',
        ], [
            'key' => 'livestreaming_setup',
            'title' => 'Livestreaming Setup',
            'icon' => 'ti ti-cast',
            'path' => '/vipanel/v2/livestreaming-setup',
        ], [
            'key' => 'registration_management',
            'title' => 'Registration Management',
            'icon' => 'ti ti-users',
            'path' => '/vipanel/v2/registration-management',
        ], [
            'key' => 'interactive_tools',
            'title' => 'Interactive Tools',
            'icon' => 'ti ti-axe',
            'path' => '/vipanel/v2/interactive-tools',
        ], [
            'key' => 'live_webinar_reports',
            'title' => 'Live Webinar Reports',
            'icon' => 'ti ti-report',
            'path' => '/vipanel/v2/live-webinar-reports',
        ], [
            'key' => 'transactional_emails',
            'title' => 'Transactional Emails',
            'icon' => 'ti ti-mail',
            'path' => '/vipanel/v2/transactional-emails',
        ],
    ];
    /*
     *  Buraya yeni bir sidebar item ekleyeceğiniz zaman aşağıdakileri yapmalısınız;
     *      1-  website/application/widgets/vipanel_v2/models/M_auth.php dosyasının içerisinde sisteme tanımlı kullanıcılar için yetki vermelisiniz.
     *      2-  website/application/widgets/vistreampanel_v2/controllers/Vi_vipanel.php &&
     *          website/application/widgets/vipanel_v2/controllers/Vi_admin_settings.php
     *          buradaki dosyaların içerisine linkin tanımlanması gerekiyor. Custom kullanıcılar için vistreampanelden ayarlanan izinleri yönetir.
     *  Not: Tanımlama yaparken "key" parametresini baz alın!
     */

    public $current_path;

    function __construct()
    {
        parent::__construct();
        $full_url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") .
            "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
        $parsed_url = parse_url($full_url);

        $this->check_logged('site_vipanel', '/vipanel/v2/login');
        $this->data['setLayout'] = null;

        $user = $this->session->site_vipanel;
        if (!($user['is_admin'] ?? false)) {
            $this->update_permissions();
            $user = $this->session->site_vipanel;
        }
        $permissions = $user['permissions'];
        $permissionList = [];
        foreach ($permissions as $key => $value) {
            foreach ($value as $k => $v) {
                $permissionList[$key][$k] = $v == "on";
            }
        }
        $this->permissions = $permissionList;

        $editSidebar = [];

        foreach ($this->sidebar as $i => $item) {
            $editSidebar[$i] = $item;
            $editSidebar[$i]['permission'] = ($permissions->{$item['key']}->main_screen ?? false) == "on";
        }

        if ($this->session->userdata('vipanel_internal_user')) {
            $editSidebar[] = [
                'key' => 'admin_settings',
                'title' => 'Admin Settings',
                'icon' => 'ti ti-brand-asana',
                'path' => 'javascript:;',
                'has_arrow' => true,
                'permission' => true,
                'childMenu' => [
                    [
                        'title' => 'On Demand Video',
                        'path' => '/vipanel/v2/admin-settings/on-demand-video',
                        'has_arrow' => false,
                    ],
                    [
                        'title' => 'Raw Reports',
                        'path' => 'javascript:;',
                        'has_arrow' => true,
                        'childMenu' => [
                            [
                                'title' => 'Login',
                                'path' => '/vipanel/v2/admin-settings/reports/login_report'
                            ],
                            [
                                'title' => 'Chat',
                                'path' => '/vipanel/v2/admin-settings/reports/chat_report'
                            ],
                            [
                                'title' => 'Survey',
                                'path' => '/vipanel/v2/admin-settings/reports/survey_report'
                            ],
                            [
                                'title' => 'Embed',
                                'path' => '/vipanel/v2/admin-settings/reports/embed_report'
                            ],
                            [
                                'title' => 'Event',
                                'path' => '/vipanel/v2/admin-settings/reports/event_report'
                            ],
                            [
                                'title' => 'Polling',
                                'path' => '/vipanel/v2/admin-settings/reports/polling_report'
                            ],
                            [
                                'title' => 'Certificate',
                                'path' => '/vipanel/v2/admin-settings/reports/certificate_report'
                            ],
                            [
                                'title' => 'Register',
                                'path' => '/vipanel/v2/admin-settings/reports/register_report'
                            ],
                            [
                                'title' => 'Quiz',
                                'path' => '/vipanel/v2/admin-settings/reports/quiz_report'
                            ],
                            [
                                'title' => 'Tracked Time',
                                'path' => '/vipanel/v2/admin-settings/reports/tracked_time_report'
                            ],
                            [
                                'title' => 'Error Log',
                                'path' => '/vipanel/v2/admin-settings/reports/error_log'
                            ],
                            [
                                'title' => 'All Data Export',
                                'path' => '/vipanel/v2/admin-settings/reports/all_data_report',
                                'target' => '_blank'
                            ],
                            [
                                'title' => 'Send to Datalake',
                                'path' => 'javascript:;',
                                'onclick' => 'sendToDatalake()'
                            ],
                        ]
                    ],
                    [
                        'title' => 'Settings',
                        'path' => 'javascript:;',
                        'has_arrow' => true,
                        'childMenu' => array_merge([
                            [
                                'title' => 'Vipanel Config',
                                'path' => '/vipanel/v2/admin-settings/vipanel_config'
                            ],
                            [
                                'title' => 'Site Users',
                                'path' => '/vipanel/v2/admin-settings/site-users',
                            ],
                            [
                                'title' => 'Custom Panel Users',
                                'path' => '/vipanel/v2/admin-settings/custom-panel-users',
                            ]
                        ],
                        (project_config('custom_users_table') == "afme") ?
                        [
                            [
                                'title' => 'Afme Users',
                                'path' => '/vipanel/v2/admin-settings/afme-users',
                            ]
                        ] : []
                        )
                    ],
                ],
            ];
        }
        $this->sidebar = $editSidebar;

        $current_url = $parsed_url['path'];

        $current_item = array_search($current_url, array_column($this->sidebar, 'path'));
        if ($this->sidebar[$current_item]['permission'] === false) {
            $available_first_path = array_search(true, array_column($this->sidebar, 'permission'));
            if ($available_first_path === false) {
                redirect('/vipanel/v2/logout');
            } else {
                redirect($this->sidebar[$available_first_path]['path'] ?? '/vipanel/v2/logout');
            }
        }
        $this->current_path = $this->sidebar[$current_item];

        $this->site_timezone = project_config_default('site_timezone', 'Europe/Istanbul');
        $this->site_date_format = project_config_default('site_date_format', 'd M Y H:i');
        if (isValidDateFormat($this->site_date_format) === false) {
            $this->site_date_format = 'd M Y H:i';
        }
    }

    public function template($view, $data = [])
    {
        $this->data['debug'] = ENV != 'production';
        $this->data['sidebar'] = $this->sidebar;
        $this->data['current_user'] = $this->session->site_vipanel;
        $this->data['permissions'] = $this->permissions;
        $this->data['full_width'] = $data['full_width'] ?? false;
        $this->data['current_path'] = $this->current_path;
        $this->data['vue3'] = $data['vue3'] ?? false;

        if (!empty($data['title'])) {
            $this->data['title'] = $data['title'];
        }

        $this->load->view('layout/header', $this->data);
        if ($data['breadcrumb'] ?? false) {
            if (!empty($data['breadcrumb_list'])) {
                $this->current_path['breadcrumb_list'] = $data['breadcrumb_list'];
            }
            if (!empty($data['breadcrumb_description'])) {
                $this->current_path['breadcrumb_description'] = $data['breadcrumb_description'];
            }
            if (!empty($data['breadcrumb_button'])) {
                $this->current_path['breadcrumb_button'] = $data['breadcrumb_button'];
            }
			if (!empty($data['breadcrumb_image'])) {
                $this->current_path['breadcrumb_image'] = $data['breadcrumb_image'];
            }
            if (!empty($data['title'])) {
                $this->current_path['title'] = $data['title'];
            }
            $this->load->view('layout/breadcrumb', $this->current_path);
        }
        $data['site_date_format'] = $this->site_date_format;
        $this->load->view($view, $data);
        $this->load->view('layout/footer');
        return true;
    }

    public function update_permissions()
    {
        $this->load->model('M_auth', 'm_auth');
        $vipanel_user = $this->session->site_vipanel;
        $user = $this->m_auth->get_user(site_config('project_id'), $vipanel_user['email']);
        $data = $this->session->site_vipanel;
        $data['permissions'] = json_decode($user->permissions ?? "[]");
        $this->session->set_userdata('site_vipanel', $data);
    }

    public function log_user_action($type = null, $data = [])
    {
        $this->load->model('M_dashboard', 'm_dashboard');

        $result = false;
        try {
            switch ($type) {
                case "CONFIG_UPDATE":
                    $description = $data['new_value'] !== '' ? 'Config key changed.' : 'Config key deleted.';
                    $result = $this->m_dashboard->logUserAction($type, $data['key'], $data['old_value'], $data['new_value'], $description);
                    break;

                case "DELETE_USER":
                    $result = $this->m_dashboard->logUserAction($data['type'] ?? $type, $data['section'] ?? null, $data['old_value'] ?? null, $data['new_value'], $data['description']);
                    break;
                case "DELETE_SURVEY_QUESTION":
                    $result = $this->m_dashboard->logUserAction($data['type'] ?? $type, $data['section'] ?? null, $data['old_value'] ?? null, $data['new_value'] ?? null, $data['description']);
                    break;

                case "DELETE_POLLING_QUESTION":
                    $result = $this->m_dashboard->logUserAction($data['type'] ?? $type, $data['section'] ?? null, $data['old_value'] ?? null, $data['new_value'] ?? null, $data['description']);
                    break;
                default:
                    $result = $this->m_dashboard->logUserAction($data['type'] ?? $type, $data['section'] ?? null, $data['old_value'] ?? null, $data['new_value'] ?? null, $data['description']);
                    break;
            }
        } catch (\Exception $e) {
            $message = $e->getMessage();
        }
        return [
            'is_success' => $result !== false,
            'message' => $message ?? null,
            'insert_id' => $result !== false ? $result : 0
        ];
    }
}
