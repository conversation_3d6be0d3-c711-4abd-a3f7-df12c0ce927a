<?php
defined('BASEPATH') or exit('No direct script access allowed');
require_once __DIR__ . '/../../vendor/autoload.php';

class SendGridService
{
	const CACHE_TIME = 60 * 60; // 1 hour
	const STORAGE_DIR = APPPATH . '../storage/sendgrid_exports';
	private SendGrid $sendGrid;
	private CI_Model $model;
	private CI_Controller $CI;
	protected array $cacheMap;
	protected CI_Cache $cache;

	public function __construct($destination = null)
	{
		$this->sendGrid = new SendGrid(ENV_SENDGRID_APIKEY);
		$this->CI = get_instance();
		$this->CI->load->driver('cache', ['adapter' => 'memcached']);
		$this->cache = $this->CI->cache ?? null;
		$this->makeCacheMap();

		if (empty($destination)) {
			$this->CI->load->model('M_transactional_emails', 'm_transactional_emails');
			$this->model = $this->CI->m_transactional_emails ?? null;
		} else {
			require_once(APPPATH . 'widgets/vipanel_v2/models/M_transactional_emails.php');
			$this->model = new M_transactional_emails();
		}
	}

	#####################################
	########### SINGLE SENDS ############
	#####################################

	/**
	 * Get all single sends from SendGrid
	 *
	 * @param bool $sync
	 * @return array
	 */
	public function getSingleSends(bool $sync = false): array
	{
		$cached = $this->lastUpdateCheckByCache('single_sends');
		if (!$cached) {
			$sync = true;
		}
		try {
			if ($sync) {
				$result = json_decode(
					$this
						->sendGrid
						->client
						->marketing()
						->singlesends()
						->search()
						->post([
							'categories' => [
								site_config('url')
							]
						])
						->body()
				)->result;
				foreach ($result as $item) {
					$item->project_id = site_config('project_id');
					$item->sendgrid_id = $item->id;
					$item->categories = json_encode($item->categories);
					unset($item->id, $item->channels);
				}
				if (!empty($result)) {
					$this->model->saveTransactionalEmails($result);
				}
			}
			$result = $this->model->getTransactionalEmails();

			if (!empty($result)) {
				foreach ($result as $i => $item) {
					if ($item->status == 'scheduled' && strtotime($item->send_at) <= time()) {
						$this->getSingleSendById($item->sendgrid_id, true);
						$result[$i]->status = 'triggered';
					}
				}
			}

			if (!$cached) {
				$this->cacheSave('single_sends');
			}
		} catch (Exception $e) {
			$result = [];
		}

		return $result;
	}

	/**
	 * Get single send by id from SendGrid
	 * @param string $id
	 * @param bool $sync
	 * @return object
	 */
	public function getSingleSendById(string $id, bool $sync = false): object
	{
		$cached = $this->lastUpdateCheckByCache('single_send', $id);
		if (!$cached) {
			$sync = true;
		}
		try {
			if ($sync) {
				$result = json_decode(
					$this
						->sendGrid
						->client
						->marketing()
						->singlesends()
						->_($id)
						->get()
						->body()
				);
				if (!empty($result)) {
					$result->project_id = site_config('project_id');
					$result->sendgrid_id = $result->id;
					$result->categories = json_encode($result->categories);
					$result->send_to = json_encode($result->send_to);
					$result->email_config = json_encode($result->email_config);
					unset($result->id, $result->channels);
					$this->model->saveTransactionalEmails([$result]);
				}
			}
			$result = $this->model->getSingleSendById($id);

			if (!$cached) {
				$this->cacheSave('single_send', $id);
			}
		} catch (Exception $e) {
			$result = [];
		}
		return $result;
	}

	/**
	 * Get single send stats from SendGrid
	 * @param string $sendgrid_id
	 * @param bool $sync
	 * @return object
	 */
	public function getSingleSendStats(string $sendgrid_id, bool $sync = false): object
	{
		$cached = $this->lastUpdateCheckByCache('single_send_stats', $sendgrid_id);
		if (!$cached) {
			$sync = true;
		}
		try {
			if ($sync) {
				$result = json_decode($this
					->sendGrid
					->client
					->marketing()
					->stats()
					->singlesends()
					->_($sendgrid_id)
					->get(null, [])->body())
					->results ?? [];
				if (!empty($result)) {
					$save = [];
					$save[0]['sendgrid_id'] = $sendgrid_id;
					$save[0]['stats'] = json_encode($result);
					$this->model->saveTransactionalEmails($save);
				}
			}
			$result = $this->model->getSingleSendById($sendgrid_id);
			$result = json_decode($result->stats ?? "[]");

			if (!$cached) {
				$this->cacheSave('single_send_stats', $sendgrid_id);
			}
		} catch (Exception $e) {
			$result = [];
		}
		return (object)$result;
	}

	/**
	 * Get single send link stats from SendGrid
	 * @param string $sendgrid_id
	 * @param bool $sync
	 * @return object
	 */
	public function getSingleSendLinkStats(string $sendgrid_id, bool $sync = false): object
	{
		$cached = $this->lastUpdateCheckByCache('single_send_link_stats', $sendgrid_id);
		if (!$cached) {
			$sync = true;
		}
		try {
			if ($sync) {
				$result = json_decode($this
					->sendGrid
					->client
					->marketing()
					->stats()
					->singlesends()
					->_($sendgrid_id)
					->links()
					->get(null, [])
					->body());
				if (!empty($result)) {
					$save = [];
					$save[0]['sendgrid_id'] = $sendgrid_id;
					$save[0]['link_stats'] = json_encode($result);
					$this->model->saveTransactionalEmails($save);
				}
			}
			$result = $this->model->getSingleSendById($sendgrid_id);
			$result = json_decode($result->link_stats ?? "[]");

			if (!$cached) {
				$this->cacheSave('single_send_link_stats', $sendgrid_id);
			}
		} catch (Exception $e) {
			$result = [];
		}
		return (object)$result;
	}

	/**
	 * Create single send email from SendGrid
	 * @param string $name
	 * @return bool
	 */
	public function createSingleSend(string $name): bool
	{
		$status = false;
		try {
			$result = json_decode($this
				->sendGrid
				->client
				->marketing()
				->singlesends()
				->post([
					'name' => $name,
					'categories' => [
						site_config('url')
					]
				])
				->body());
			if (!empty($result)) {
				$result->project_id = site_config('project_id');
				$result->sendgrid_id = $result->id;
				$result->categories = json_encode($result->categories);
				$result->send_to = json_encode($result->send_to);
				$result->email_config = json_encode($result->email_config);
				unset($result->id, $result->channels);
				$this->model->saveTransactionalEmails([$result]);
				$status = true;
			}
		} catch (Exception $e) {
			$status = false;
		}
		return $status;
	}

	/**
	 * Update single send email from SendGrid
	 * @param string $id
	 * @param array $data
	 * @param bool $publish
	 * @return bool
	 */
	public function updateSingleSend(string $id, array $data): bool
	{
		unset($data['id'], $data['publish']);

		try {
			$content = $data['content'] ?? "";
			if (!empty($template_id = $data['template_id'])) {
				$content = get_page(site_config('project_id'), $template_id)->content;
			}
			$list_ids = is_array($data['list_ids']) ? $data['list_ids'] : [$data['list_ids']];
			$status = 'draft';
			$query = [
				"name" => $data['name'],
				"status" => $status,
				"categories" => [
					site_config('url')
				],
				"send_at" => null,
				"send_to" => [
					"list_ids" => $list_ids
				],
				"email_config" => [
					"subject" => $data['subject'] ?? "",
					"html_content" => $content,
					"suppression_group_id" => null,
					"custom_unsubscribe_url" => $data['unsubscribe_link'] ?? ('https://' . site_config('url') . '/unsubscribe'),
					"sender_id" => (int)$data['sender_id'],
					"ip_pool" => null
				]
			];
			if ($data['send_at']) {
				$send_at = new DateTime($data['send_at']);
				$query['send_at'] = $send_at->format("Y-m-d\TH:i:s\Z");
			}
			$result = $this
				->sendGrid
				->client
				->marketing()
				->singlesends()
				->_($id)
				->patch($query);
			if (in_array($result->statusCode(), [200, 201, 202, 203, 204])) {
				$this->model->saveTransactionalEmails([
					[
						'project_id' => site_config('project_id'),
						'sendgrid_id' => $id,
						'temp_content' => !empty($template_id) ? 'TEMPLATE:' . $template_id : $content,
						'updated_at' => date('Y-m-d H:i:s')
					]
				]);
				$this->getSingleSendById($id, true);
				$status = true;
			} else {
				$status = false;
			}
		} catch (Exception $e) {
			$status = false;
		}
		return $status ?? false;
	}

	/**
	 * Publish single send email from SendGrid
	 * @param string $id
	 * @return bool
	 */
	public function publishSingleSend(string $id): array
	{
		try {
			$data = $this->model->getSingleSendById($id);
			$send_at = 'now';
			if (!empty($data->send_at)) {
				$send_at = new DateTime(
					$data->send_at,
					new DateTimeZone(
						project_config_default(
							'site_timezone',
							'Europe/Istanbul'
						)
					)
				);
				$send_at = $send_at->format(DateTime::ATOM);
			}
			$result = $this
				->sendGrid
				->client
				->marketing()
				->singlesends()
				->_($id)
				->schedule()
				->put([
					"send_at" => $send_at
				]);
			if (in_array($result->statusCode(), [200, 201, 202, 203, 204])) {
				$this->getSingleSendById($id, true);
				$status = true;
			} else {
				$message = json_decode($result->body())->errors[0]->message ?? "Unknown error";
			}
		} catch (Exception $e) {
			$status = false;
			$message = $e->getMessage();
		}
		return [
			'status' => $status ?? false,
			'message' => $message ?? ""
		];
	}

	/**
	 * Cancel single send email from SendGrid
	 * @param string $id
	 * @return bool
	 */
	public function cancelScheduledSingleSend(string $id): bool
	{
		try {
			$result = $this
				->sendGrid
				->client
				->marketing()
				->singlesends()
				->_($id)
				->schedule()
				->delete();
			$status = in_array($result->statusCode(), [200, 201, 202, 203, 204]);
			$this->getSingleSendById($id, true);
		} catch (Exception $e) {
			$status = false;
		}
		return $status;
	}

	public function deleteSingleSend(string $id): bool
	{
		try {
			$result = $this
				->sendGrid
				->client
				->marketing()
				->singlesends()
				->_($id)
				->delete();
			$status = in_array($result->statusCode(), [200, 201, 202, 203, 204]);
			$this->model->deleteSingleSendById($id, true);
		} catch (Exception $e) {
			$status = false;
		}
		return $status;
	}

	/**
	 * Get list by form name
	 *
	 * @param array $formNames
	 * @return array
	 */
	public function getRelatedLists(array $formNames): array
	{
		return $this->model->getRelatedLists($formNames);
	}


	#####################################
	############# SENDERS ###############
	#####################################

	/**
	 * Get all senders from SendGrid
	 * @param bool $sync
	 * @param string $search
	 * @return array
	 */
	public function getSenders(bool $sync = false, string $search = ""): array
	{
		$cached = $this->lastUpdateCheckByCache('single_send_senders');
		if (!$cached) {
			$sync = true;
		}
		try {
			if ($sync) {
				$result = json_decode(
					$this
						->sendGrid
						->client
						->marketing()
						->senders()
						->get()
						->body()
				);
				$this->model->saveSenders($result);
			}
			$result = $this->model->getSenders($search);

			if (!$cached) {
				$this->cacheSave('single_send_senders');
			}
		} catch (Exception $e) {
			$result = [];
		}
		return $result;
	}

	/**
	 * Create a new sender
	 * @param string $name
	 * @param string $email
	 * @param string $reply_to
	 * @return bool
	 */
	public function createSender(string $name, string $email, string $reply_to): bool
	{
		try {
			$result = $this
				->sendGrid
				->client
				->marketing()
				->senders()
				->post([
					"nickname" => "API - " . strtotime('now'),
					"from" => [
						"email" => $email,
						"name" => $name
					],
					"reply_to" => [
						"email" => $reply_to
					],
					"address" => "Ortakoy Mahallesi Sair Necati Sokak No:33",
					"city" => "Istanbul",
					"country" => "TUR"
				]);
			$status = in_array($result->statusCode(), [200, 201, 202, 203, 204]);
			if ($status) {
				$this->getSenders(true);
			}
		} catch (Exception $e) {
			$status = false;
		}
		return $status;
	}


	#####################################
	######### CONTACTS & LISTS ##########
	#####################################

	/**
	 * Get all lists from SendGrid
	 * @param bool $sync
	 * @return array
	 */
	public function getLists(bool $sync = false): array
	{
		$cached = $this->lastUpdateCheckByCache('single_send_contacts');
		if (!$cached) {
			$sync = true;
		}
		try {
			if ($sync) {
				$result = json_decode(
					$this
						->sendGrid
						->client
						->marketing()
						->lists()
						->get(null, [
							'pageSize' => 1000
						])
						->body()
				)->result ?? [];
				foreach ($result as $i => $item) {
					if (!str_contains($item->name, site_config('url'))) {
						unset($result[$i]);
						continue;
					}
					$name = explode('|', $item->name);
					$item->name = trim($name[1] ?? $name[0]);
					$item->project_id = !empty($name[1]) ? (get_project_by_url(trim($name[0]))->id ?? 0) : site_config('project_id');
					$item->sendgrid_id = $item->id;
					$item->last_update_time = date('Y-m-d H:i:s');
					unset($item->id, $item->_metadata);
				}
				if (!empty($result)) {
					$this->model->saveLists($result);
				}
			}
			$result = $this->model->getLists();

			if (!$cached) {
				$this->cacheSave('single_send_contacts');
			}
		} catch (Exception $e) {
			$result = [];
			dd($e->getMessage());
		}
		return $result;
	}

	/**
	 * Get list by id from SendGrid
	 * @param string $id
	 * @param bool $sync
	 * @return array
	 */
	public function getListById(string $id, bool $sync = true): array
	{
		try {
			if ($sync) {
				$result = json_decode(
					$this
						->sendGrid
						->client
						->marketing()
						->lists()
						->_($id)
						->get(null, [
							'contact_sample' => true
						])
						->body()
				);
				if (!empty($result)) {
					$result->name = str_replace(site_config('url') . ' | ', '', $result->name);
					$result->project_id = site_config('project_id');
					$result->sendgrid_id = $result->id;
					$result->last_update_time = date('Y-m-d H:i:s');
					unset($result->id, $result->_metadata, $result->identifier_counts);
					$this->model->saveLists([$result]);
				}
			}
			$result = $this->model->getListById($id);
		} catch (Exception $e) {
			$result = [];
		}
		return $result ?? [];
	}

	/**
	 * Create a new list
	 * @param string $name
	 * @return array
	 */
	public function createList(string $name): array
	{
		try {
			$check = $this->model->getListByName($name);
			if (!empty($check)) {
				return [
					'status' => true,
					'message' => 'List already exists'
				];
			}
			$result = $this->sendGrid->client->marketing()->lists()->post([
				'name' => site_config('url') . ' | ' . $name
			]);
			if (in_array($result->statusCode(), [200, 201, 202])) {
				$this->getListById(
					json_decode($result->body())->id
				);
			}
			$result = json_decode($result->body(), true);
		} catch (Exception $e) {
			$result = [];
		}
		return $result;
	}

	/**
	 * Delete a list
	 * @param string $id
	 * @return bool
	 */
	public function deleteList(string $id): bool
	{
		try {
			$result = $this->sendGrid->client->marketing()->lists()->_($id)->delete();
			if (in_array($result->statusCode(), [200, 201, 202, 203, 204])) {
				$status = $this->model->deleteList($id);
			}
			$result = $status ?? false;
		} catch (Exception $e) {
			$result = false;
		}
		return $result;
	}

	/**
	 * Create a CSV file for contacts
	 * @param string $id
	 * @return bool
	 */
	public function exportContacts(string $id): bool
	{
		try {
			$result = json_decode($this
				->sendGrid
				->client
				->marketing()
				->contacts()
				->exports()
				->post([
					"list_ids" => [
						$id
					],
					"segment_ids" => [],
					"notifications" => [
						"email" => false
					]
				])
				->body());
			$this->model->saveContactExports($id, ['id' => $result->id]);
			return true;
		} catch (Exception $e) {
			return false;
		}
	}

	/**
	 * Create a CSV file for all contacts
	 * @param string $id
	 * @return bool
	 */
	public function exportAllContacts(): bool
	{
		try {
			$result = json_decode($this
				->sendGrid
				->client
				->marketing()
				->contacts()
				->exports()
				->post([
					"list_ids" => [],
					"segment_ids" => [],
					"notifications" => [
						"email" => false
					]
				])
				->body());
			$this->model->saveQueueItem([
				'project_id' => null,
				'name' => 'export_all_contacts',
				'job_id' => $result->id,
				'related_id' => null,
				'extra' => null,
				'created_at' => date('Y-m-d H:i:s')
			]);
			return true;
		} catch (Exception $e) {
			return false;
		}
	}

	/**
	 * Sync statuses and get exporting count
	 * @return int
	 */
	public function getExportingCount(): int
	{
		$count = 0;
		try {
			$data = $this->model->getContactExporting();
			foreach ($data as $datum) {
				$id = json_decode($datum->export_urls ?? "[]")->id ?? null;
				$result = json_decode($this
					->sendGrid
					->client
					->marketing()
					->contacts()
					->exports()
					->_($id)
					->get()
					->body());
				if ($result->status === 'ready') {
					$this->model->saveContactExports(
						$datum->sendgrid_id,
						$result->urls,
						0
					);
				} else {
					$count++;
				}
			}
			return $count;
		} catch (Exception $e) {
		}
		return $count;
	}

	/**
	 * Get importing count
	 * @param string $id
	 * @return int
	 */
	public function getImportingCount(string $id): int
	{
		$count = 0;
		try {
			$data = $this->model->getQueueItemByRelatedId($id);
			$data = array_filter($data, function ($item) {
				return $item->status == null;
			});
			$count = count($data);
			if (!empty($data)) {
				$data = array_map(function ($item) {
					return $item->job_id;
				}, $data);
				foreach ($data as $item) {
					$result = json_decode($this
						->sendGrid
						->client
						->marketing()
						->contacts()
						->imports()
						->_($item)
						->get()
						->body());
					if (in_array($result->status, ['completed', 'errored'])) {
						$this->model->updateQueueItem($item, [
							'status' => $result->status,
							'response' => json_encode($result)
						]);
						$count--;
					}
					sleep(2);
				}
			}
		} catch (Exception $e) {
			dd($e->getMessage());
		}
		return $count;
	}

	public function retrieveExports()
	{
		if (!file_exists(self::STORAGE_DIR)) {
			mkdir(self::STORAGE_DIR, 0755, true);
		}
		$lists = $this->getLists();
		foreach ($lists as $list) {
			if (!empty($list->export_urls = json_decode($list->export_urls))) {
				foreach ($list->export_urls as $url) {
					print_r("Downloading: " . $url . PHP_EOL);

					$compressedContent = file_get_contents($url);
					if ($compressedContent !== false) {
						// Extract original filename from URL
						$urlParts = parse_url($url);
						$pathParts = pathinfo($urlParts['path']);
						$originalFilename = isset($pathParts['filename']) ? $pathParts['filename'] : 'sendgrid_export_' . time();

						// Create storage directory if it doesn't exist
						if (!file_exists(self::STORAGE_DIR)) {
							mkdir(self::STORAGE_DIR, 0755, true);
						}

						$tempFile = tempnam(sys_get_temp_dir(), 'sendgrid_csv_');
						file_put_contents($tempFile, $compressedContent);
						print_r("File downloaded and saved to: " . $tempFile . PHP_EOL);

						// Check if the file is gzip compressed (based on URL or content)
						$isGzipped = (strpos($url, '.gzip') !== false) ||
							(substr($compressedContent, 0, 2) === "\x1f\x8b");

						if ($isGzipped) {
							print_r("Detected gzip compressed file. Decompressing..." . PHP_EOL);
							// Decompress the content
							$decompressedFile = tempnam(sys_get_temp_dir(), 'sendgrid_csv_decompressed_');

							// Use gzdecode if available, otherwise use shell command
							if (function_exists('gzdecode')) {
								$decompressedContent = gzdecode($compressedContent);
								if ($decompressedContent !== false) {
									file_put_contents($decompressedFile, $decompressedContent);
									print_r("File decompressed successfully using gzdecode." . PHP_EOL);
								} else {
									print_r("Failed to decompress with gzdecode. Trying shell command..." . PHP_EOL);
									shell_exec("gzip -dc $tempFile > $decompressedFile");
								}
							} else {
								// Use shell command as fallback
								shell_exec("gzip -dc $tempFile > $decompressedFile");
								print_r("File decompressed using shell command." . PHP_EOL);
							}

							// Use the decompressed file for CSV processing
							$csvFile = $decompressedFile;
						} else {
							// Use the original file if not compressed
							$csvFile = $tempFile;
						}

						// Read the CSV file and convert to JSON
						if (($handle = fopen($csvFile, 'r')) !== false) {
							// Read header row first
							$headers = fgetcsv($handle, 1000, ',');
							if ($headers) {
								print_r("CSV Headers: " . PHP_EOL);
								// print_r($headers);
								// print_r(PHP_EOL);

								// Read all data rows and convert to JSON
								$jsonData = [];
								$rowCount = 0;

								// Preview first 5 rows
								// print_r("First 5 rows of data: " . PHP_EOL);
								while (($data = fgetcsv($handle, 1000, ',')) !== false) {
									// Create associative array using headers as keys
									$rowData = [];
									foreach ($headers as $index => $header) {
										if (isset($data[$index])) {
											$rowData[$header] = $data[$index];
										}
									}

									// Add to JSON data array
									$jsonData[] = $rowData;

									// Print preview of first 5 rows
									if ($rowCount < 5) {
										// print_r($data);
									}

									$rowCount++;
								}

								print_r("Total rows in CSV: " . $rowCount . PHP_EOL);

								// Save as JSON file
								$jsonFilePath = self::STORAGE_DIR . '/' . $list->sendgrid_id . '.json';
								file_put_contents($jsonFilePath, json_encode($jsonData, JSON_PRETTY_PRINT));
								print_r("Data saved as JSON to: " . $jsonFilePath . PHP_EOL);

								// Also save a copy of the original CSV
								// $csvFilePath = self::STORAGE_DIR . '/' . $originalFilename . '.csv';
								// copy($csvFile, $csvFilePath);
								// print_r("Original CSV saved to: " . $csvFilePath . PHP_EOL);
							}
							fclose($handle);
						} else {
							print_r("Failed to open CSV file for reading." . PHP_EOL);
						}

						// Clean up temporary files
						unlink($tempFile);
						if (isset($decompressedFile) && file_exists($decompressedFile)) {
							unlink($decompressedFile);
						}
					} else {
						print_r("Failed to download file from: " . $url . PHP_EOL);
					}
					sleep(5);
				}
			}
		}
	}

	public function saveExportLists()
	{
		$lists = [];
		$files = glob(APPPATH . '../storage/sendgrid_exports/*.json');
		foreach ($files as $file) {
			$fileName = basename($file, '.json');
			$fileName = explode('.', $fileName)[0];
			$data = json_decode(file_get_contents($file), true);
			foreach ($data as $row) {
				$lists[] = [
					'list_id' => $fileName,
					'email' => $row['EMAIL'],
					'contact_id' => $row['CONTACT_ID'],
					'data' => json_encode($row)
				];
			}
			unlink($file);
		}
		return $this->model->saveExportedContacts($lists);
	}

	/**
	 * Add contacts to a list
	 * @param string $list_id
	 * @param array $data
	 * @param string $form_name
	 * @return bool
	 */
	public function addContacts(string $list_id, array $data, string $form_name = ""): bool
	{
		if (empty($form_name) || in_array($form_name, ['', null, '0', 'All Registers'])) {
			$form_name = null;
		}
		if (!empty($data)) {
			$data = array_map(function ($item) {
				$item['project_id'] = (int) site_config('project_id');
				return $item;
			}, $data);
		}
		try {
			$result = json_decode($this
				->sendGrid
				->client
				->marketing()
				->contacts()
				->put([
					"list_ids" => [
						$list_id
					],
					"contacts" => $data
				])->body());
			if (!empty($result->job_id)) {
				$this->model->updateList($list_id, [
					'related_form' => $form_name,
					'last_update_time' => date('Y-m-d H:i:s')
				]);
				$this->model->saveQueueItem([
					'project_id' => site_config('project_id'),
					'name' => 'add_contacts',
					'job_id' => $result->job_id,
					'related_id' => $list_id,
					'extra' => json_encode($data),
					'created_at' => date('Y-m-d H:i:s')
				]);
				return true;
			}
			throw new Exception($result->errors[0]->message ?? "Unknown error");
		} catch (Exception $e) {
			errorLog('Sendgrid - Add Contact', $e->getMessage());
		}
		return false;
	}

	/**
	 * Remove contacts from a list
	 * @param string $contact_id
	 * @param string $email
	 * @param string|null $list_id
	 * @param string|null $source
	 * @return bool
	 */
	public function removeContacts(string $contact_id, string $email, string $list_id = null, string $source = null): bool
	{
		try {
			if (empty($list_id)) {
				$url = 'https://api.sendgrid.com/v3/marketing/contacts?ids=' . $contact_id;
			} else {
				$url = 'https://api.sendgrid.com/v3/marketing/lists/' . $list_id . '/contacts?contact_ids=' . $contact_id;
			}
			$result = $this->customRequest($url, 'DELETE');
			if (!empty($result->job_id)) {
				$this->model->saveQueueItem([
					'project_id' => site_config('project_id'),
					'name' => 'remove_contacts',
					'job_id' => $result->job_id,
					'related_id' => $list_id,
					'extra' => json_encode([
						'contact_id' => $contact_id,
						'email' => $email,
						'source' => $source
					]),
					'created_at' => date('Y-m-d H:i:s')
				]);
				return true;
			}
		} catch (Exception $e) {
		}
		return false;
	}

	public function removeContactBatch(array $ids): bool
	{
		try {
			$result = json_decode($this
				->sendGrid
				->client
				->marketing()
				->contacts()
				->delete(null, [
					'delete_all_contacts' => false,
					'ids' => implode(',', $ids)
				])
				->body());
			if (!empty($result->job_id)) {
				$this->model->saveQueueItem([
					'project_id' => null,
					'name' => 'remove_contacts',
					'job_id' => $result->job_id,
					'created_at' => date('Y-m-d H:i:s')
				]);
			}
			return true;
		} catch (\Exception $e) {
			return false;
		}
	}

	/**
	 * Get removing count
	 * @param string $id
	 * @return int
	 */
	public function getRemovingCount(string $id): int
	{
		$count = 0;
		try {
			$data = $this->model->getQueueItemByRelatedId($id);
			$data = array_filter($data, function ($item) {
				return $item->status == null;
			});
			$count = count($data);
			if (!empty($data)) {
				$data = array_map(function ($item) {
					return $item->job_id;
				}, $data);
				foreach ($data as $item) {
					$result = json_decode($this
						->sendGrid
						->client
						->marketing()
						->contacts()
						->imports()
						->_($item)
						->get()
						->body());
					if ($result->status != 'pending') {
						$this->model->updateQueueItem($item, [
							'status' => $result->status,
							'response' => json_encode($result)
						]);
						$count--;
					}
					sleep(2);
				}
			}
		} catch (Exception $e) {
			dd($e->getMessage());
		}
		return $count;
	}

	/**
	 * Update list related form
	 * @param string $id
	 * @param string $form_name
	 * @return bool
	 */
	public function updateListForm(string $id, string $form_name = ""): bool
	{
		if (empty($form_name) || in_array($form_name, ['', null, '0', 'All Registers'])) {
			$form_name = null;
		}
		try {
			return $this->model->updateList($id, [
				'related_form' => $form_name
			]);
		} catch (Exception $e) {
			return false;
		}
	}

	/**
	 * Search contact by email
	 *
	 * @param string $email
	 * @param string|null $list_id
	 * @return array
	 */
	public function searchContact(string $email, string $list_id = null): array
	{
		try {
			$result = json_decode($this
				->sendGrid
				->client
				->marketing()
				->contacts()
				->search()
				->post([
					'query' => empty($list_id) ?
						"email LIKE '" . $email . "%'" :
						"email LIKE '" . $email . "%' AND CONTAINS(list_ids, '" . $list_id . "')"
				])
				->body());
			if (($result->contact_count ?? 0) == 1) {
				$result = (array)$result->result[0];
			} else {
				$result = [];
			}
		} catch (Exception $e) {
			$result = [];
		}
		return $result ?? [];
	}

	/**
	 * Get sample contact list by id
	 * @param string $id
	 * @return array
	 */
	public function getSampleContactList(string $id): array
	{
		$curl = curl_init();
		curl_setopt_array($curl, array(
			CURLOPT_URL => 'https://api.sendgrid.com/v3/marketing/lists/' . $id . '?contact_sample=true',
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => '',
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 0,
			CURLOPT_FOLLOWLOCATION => true,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => 'GET',
			CURLOPT_HTTPHEADER => array(
				'Authorization: Bearer ' . ENV_SENDGRID_APIKEY
			),
		));
		$response = curl_exec($curl);
		curl_close($curl);
		return json_decode($response, true);
	}

	/**
	 * Get unsubscribe list
	 * @return array
	 */
	public function getUnsubscribeList(): array
	{
		return $this->model->getUnsubscribeList();
	}

	public function getContactLimits(bool $sync = false): array
	{
		// $this->cacheDelete('sendgrid_contact_limit');
		$cached = $this->lastUpdateCheckByCache('sendgrid_contact_limit');
		if (!$cached) {
			$sync = true;
		}
		try {
			if ($sync) {
				$result = json_decode($this->sendGrid->client
					->marketing()
					->contacts()
					->count()
					->get()
					->body(), true);
				$this->cacheSave('sendgrid_contact_limit');
				$this->cacheSave('sendgrid_contact_limit_data', '', $result);
			} else {
				$cached = $this->getCache('sendgrid_contact_limit_data');
				$result = $cached;
			}
			return $result;
		} catch (\Exception $e) {
			return [];
		}
	}


	####################################################
	############# MAIL TEST & TEMPLATES ################
	####################################################

	/**
	 * Send test email
	 * @param string $template_id
	 * @param array $emails
	 * @return bool
	 */
	public function sendTestEmail(string $template_id, array $emails): bool
	{
		try {
			$version = $this->createTemplateVersion($template_id);
			if (empty($version)) {
				return false;
			}
			$template_id = $version['template_id'];
			$result = $this
				->sendGrid
				->client
				->marketing()
				->test()
				->send_email()
				->post([
					"template_id" => $template_id,
					"sender_id" => $version['sender_id'],
					"custom_unsubscribe_url" => null,
					"emails" => $emails
				]);
			if (in_array($result->statusCode(), [200, 201, 202, 203, 204])) {
				$result = $this->deleteTemplateVersion($template_id, $version['id']);
			}
		} catch (Exception $e) {
			$result = false;
		}
		return $result;
	}

	/**
	 * Create template version
	 * @param string $template_id
	 * @return array
	 */
	private function createTemplateVersion(string $template_id): array
	{
		try {
			$data = $this->model->getSingleSendById($template_id);
			$content = json_decode($data->email_config);
			$result = $this
				->sendGrid
				->client
				->templates()
				->_('d-bcbf40513b1542a195e681a8750b3c7b')
				->versions()
				->post([
					"active" => 1,
					"editor" => $content->editor,
					"html_content" => $content->html_content,
					"name" => site_config('url') . " | Mail Version: " . now(),
					"generate_plain_content" => true,
					"subject" => $content->subject,
				]);
			if (in_array($result->statusCode(), [200, 201, 202, 203, 204])) {
				$result = json_decode($result->body(), true);
				$result['sender_id'] = $content->sender_id;
			} else {
				$result = [];
			}
		} catch (Exception $e) {
			$result = [];
		}
		return $result;
	}

	/**
	 * Delete template version
	 * @param string $template_id
	 * @param string $version_id
	 * @return bool
	 */
	private function deleteTemplateVersion(string $template_id, string $version_id): bool
	{
		try {
			$result = $this
				->sendGrid
				->client
				->templates()
				->_($template_id)
				->versions()
				->_($version_id)
				->delete();
			$result = in_array($result->statusCode(), [200, 201, 202, 203, 204]);
		} catch (Exception $e) {
			$result = false;
		}
		return $result;
	}


	#####################################
	############# OTHERS ################
	#####################################

	public function getCache($key)
	{
		return $this->cache->get($key);
	}

	/**
	 * Get last update time
	 * @param string $cacheKey
	 * @param string|null $id
	 * @return int
	 */
	public function getLastUpdate(string $cacheKey, string $id = ''): int
	{
		$key = $this->cacheMap[$cacheKey];
		if (!empty($id)) {
			$key = $key . '_' . $id;
		}
		return $this->cache->get($key);
	}

	/**
	 * Check if cache is still valid
	 * @param string $cacheKey
	 * @param string|null $id
	 * @return bool
	 */
	public function lastUpdateCheckByCache(string $cacheKey, string $id = ''): bool
	{
		$key = $this->cacheMap[$cacheKey] ?? null;
		if (!empty($id)) {
			$key = $key . '_' . $id;
		}
		$currentTime = time();
		if (!empty($lastAccessTime = $this->cache->get($key))) {
			$elapsedTime = $currentTime - $lastAccessTime;
			return $elapsedTime <= self::CACHE_TIME;
		}
		return false;
	}

	/**
	 * Save cache by key
	 * @param string $cacheKey
	 * @param string|null $id
	 * @return void
	 */
	private function cacheSave(string $cacheKey, string $id = '', array $data = []): void
	{
		$key = $this->cacheMap[$cacheKey] ?? null;
		if (!empty($id)) {
			$key = $key . '_' . $id;
		}
		$this->cache->save($key, !empty($data) ? $data : time(), 3600);
	}

	/**
	 * Delete cache by key
	 * @param string $cacheKey
	 * @param string|null $id
	 * @return void
	 */
	public function cacheDelete(string $cacheKey, string $id = ''): void
	{
		$key = $this->cacheMap[$cacheKey] ?? null;
		if (!empty($id)) {
			$key = $key . '_' . $id;
		}
		$this->cache->delete($key);
	}

	/**
	 * Make cache map
	 * @return void
	 */
	private function makeCacheMap(): void
	{
		$this->cacheMap = [
			'single_send' => 'single_send_' . site_config('project_id'),
			'single_sends' => 'single_sends_' . site_config('project_id'),
			'single_send_stats' => 'single_send_stats_' . site_config('project_id'),
			'single_send_link_stats' => 'single_send_link_stats_' . site_config('project_id'),
			'single_send_contacts' => 'single_send_contacts_' . site_config('project_id'),
			'single_send_senders' => 'senders_' . site_config('project_id'),
			'sendgrid_contact_limit' => 'sendgrid_contact_limit',
			'sendgrid_contact_limit_data' => 'sendgrid_contact_limit_data',
		];
	}

	/**
	 * Get queue list
	 * @param string $sendgrid_id
	 * @param string $name
	 * @return array
	 */
	public function getQueueList(string $sendgrid_id, string $name = ''): array
	{
		return $this->model->getQueueList($sendgrid_id, $name);
	}

	/**
	 * Get register list
	 * @param string|null $form_name
	 * @return array
	 */
	public function retrieveRegister(string $form_name = ''): array
	{
		return $this->model->retrieveRegister($form_name);
	}

	/**
	 * Get template by url
	 * @param string $template_url
	 * @return array
	 */
	public function retrieveTemplate(string $template_url): object
	{
		return $this->model->retrieveTemplate($template_url);
	}

	/**
	 * Get magical list
	 * @param string $type
	 * @param string|null $form_name
	 * @return array
	 */
	public function retrieveMagicalList(string $type, string $form_name = ''): array
	{
		switch ($type) {
			case 'reminder':
				$result = $this->model->retrieveReminderList($form_name);
				break;
			case 'thank-you':
				$result = $this->model->retrieveThankYouList($form_name);
				break;
			case 'miss-you':
				$result = $this->model->retrieveMissYouList($form_name);
				break;
			default:
				$result = [];
				break;
		}

		return array_map(function ($item) {
			$i = [];
			$fullname = explode(' ', $item->fullname);
			$i['email'] = $item->email;
			$i['first_name'] = $item->first_name ?? (reset($fullname) ?? "-");
			$i['last_name'] = $item->last_name ?? (end($fullname) ?? "-");
			return $i;
		}, $result);
	}

	/**
	 * Save template
	 * @param int $id
	 * @param string $content
	 * @return bool
	 */
	public function saveTemplate(int $id, string $content): bool
	{
		return $this->model->saveTemplate($id, htmlspecialchars_decode($content));
	}

	/**
	 * Send manual request to SendGrid
	 *
	 * @param string $request_url
	 * @param string $request_type
	 * @param array $data
	 * @return object
	 */
	public function customRequest(string $request_url, string $request_type = 'GET', array $data = []): object
	{
		$curl = curl_init();

		curl_setopt_array($curl, array(
			CURLOPT_URL => $request_url,
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => '',
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 0,
			CURLOPT_FOLLOWLOCATION => true,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => $request_type,
			CURLOPT_POSTFIELDS => json_encode($data),
			CURLOPT_HTTPHEADER => array(
				'Accept: application/json',
				'Authorization: Bearer ' . ENV_SENDGRID_APIKEY
			),
		));

		$response = curl_exec($curl);

		curl_close($curl);
		return json_decode($response);
	}

	public function clearUnsedContacts()
	{
		$list = $this->model->retrieveUnusedContacts();
		$list = array_map(function ($item) {
			return $item->contact_id;
		}, $list);

		if (!empty($list)) {
			$this->removeContactBatch($list);
		}
	}
}
