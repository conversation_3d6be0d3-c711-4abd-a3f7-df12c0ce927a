<?php

function getStreamsDropdown()
{
	$CI = get_instance();
	$project_id = $CI->session->userdata('site_config')->project_id;
	$stream_lang = $CI->session->userdata('site_config')->stream_lang;
	$where = [
		'project_id' => $project_id
	];
	$CI->db->select('lang,name');
	$CI->db->where($where);
	$CI->db->from('embed');
	$CI->db->order_by('order', 'asc');
	$result = $CI->db->get()->result();
	$html = '';
	if (count($result)) {
		$html .= '<select class="streamLanguages">';
		foreach ($result as $r) {
			$selected = '';
			if ($stream_lang == $r->lang) {
				$selected = 'selected';
			}
			$html .= '<option value="' . $r->lang . '" ' . $selected . '>' . $r->name . '</option>';
		}
		$html .= '</select>';
	}
	echo $html;
}

function getSiteLanguages()
{
	$CI = get_instance();
	$languages = $CI->session->userdata('site_config')->site_languages;
	$langs = array();
	foreach ($languages as $lang) {
		$langs[$lang->id] = strtoupper($lang->iso);
	}
	return $langs;
}

function getSiteLanguageById($id)
{
	if ($id == null || empty($id)) {
		return null;
	}
	$CI = get_instance();
	$languages = $CI->session->userdata('site_config')->site_languages;
	foreach ($languages as $lang) {
		if ($id == $lang->id)
			return strtoupper($lang->iso);
	}
}

function getFlags()
{
	$CI = get_instance();
	$project_id = $CI->session->userdata('site_config')->project_id;
	$where = [
		'project_id' => $project_id
	];
	$CI->db->select('lang,name');
	$CI->db->where($where);
	$CI->db->from('embed');
	$CI->db->order_by('order', 'asc');
	$result = $CI->db->get()->result();
	$html = '';
	if (count($result)) {
		$i = 1;
		foreach ($result as $r) {
			$html .= '<div class="col-lg-4 col-md-4 col-sm-4 col-xs-12 flag_wrapper flag_' . $r->lang . '"><div class="flagItem" data-flag="' . $r->lang . '">';
			$html .= '<a href="javascript:void(0);" onclick="change_stream_language(\'' . $r->lang . '\', false);">';
			$html .= '<img src="/assets/common/general/images/flags/' . substr($r->lang, 0, 2) . '.png" alt="" class="img-responsive">';
			$html .= '</a></div><span class="mt20 db">' . $r->name . '</span></div>';
			if ($i > 2 && $i % 3 === 0) {
				$html .= '<div class="clear"></div>';
			}

			$i++;
		}
	}
	echo $html;
}

function getFlagsNew()
{
	$CI = get_instance();
	$project_id = $CI->session->userdata('site_config')->project_id;
	$where = [
		'project_id' => $project_id
	];
	$CI->db->select('lang,name');
	$CI->db->where($where);
	$CI->db->from('embed');
	$CI->db->order_by('order', 'asc');
	$result = $CI->db->get()->result();
	$html = '';
	if (count($result)) {
		$i = 1;
		foreach ($result as $r) {
			$html .= '<div class="info__lang__info flag_wrapper flag_' . $r->lang . '">';
			$html .= '<div class="info__lang__flag flagItem" data-flag="' . $r->lang . '" onclick="change_stream_language(\'' . $r->lang . '\', false);">';
			$html .= '<button>';
			$html .= '<img src="/assets/common/general/images/flags/' . substr($r->lang, 0, 2) . '.png" alt="Flag" class="info__lang__img" />';
			$html .= '</button>';
			$html .= '</div>';
			$html .= '<p class="info__lang__name" onclick="change_stream_language(\'' . $r->lang . '\', false);">' . $r->name . '</p>';
			$html .= '</div>';


			if ($i > 2 && $i % 3 === 0) {
				$html .= '<div class="clear"></div>';
			}

			$i++;
		}
	}
	echo $html;
}

function getAudience()
{
	//eskiden kalanlar için
	getAudienceNumber();
}

function getAudienceNumber()
{
	$CI = get_instance();
	if (!isset($CI->session->userdata('site_user')->nof_people)) {
		$CI->session->userdata('site_user')->nof_people = 1;
	}
	echo $CI->session->userdata('site_user')->nof_people;
}

function getAudienceNames()
{
	$CI = get_instance();
	if (!isset($CI->session->userdata('site_user')->names)) {
		$CI->session->userdata('site_user')->names = '';
	}
	echo $CI->session->userdata('site_user')->names;
}

function getOfflineVideoList()
{
	$CI = get_instance();
	$CI->load->model('M_video', 'm_video');
	$video_list = $CI->m_video->get_list($CI->session->userdata('site_config')->project_id);
	$video_id = $CI->input->get('video', true);
	$result_html = '<ul>';
	if (count($video_list)) {
		$i = 0;
		foreach ($video_list as $video) {
			$activeClass = '';
			if (($video->id == $video_id) || ($video_id && $i = 0)) {
				$activeClass = 'active';
			}

			$result_html .= '<li class="' . $activeClass . '">';
			$result_html .= '<figure>';
			$result_html .= '<a href="/offline?video=' . $video->id . '">';

			if ($video->img_url) {
				$result_html .= '<img src="/assets/static/' . $CI->session->userdata('site_config')->url . '/offline_video/' . $video->img_url . '" alt="" width="54" height="44">';
			} else {
				$result_html .= '<img src="/assets/common/general/images/video_thumbnail_default.png" alt="">';
			}

			$result_html .= '</a>';
			$result_html .= '</figure>';
			$result_html .= '<a href="/offline?video=' . $video->id . '">' . $video->title . ' <br/> ' . $video->name . '</a>';
			$result_html .= '</li>';
			$i++;
		}
	}
	$result_html .= '</ul>';
	echo $result_html;
}

function getOndemandVideoList()
{
	$CI = get_instance();
	$CI->load->model('M_video', 'm_video');
	$speciality = isset($CI->session->userdata('site_user')->speciality) ? $CI->session->userdata('site_user')->speciality : null;;
	$country = $CI->session->userdata('site_user')->country;
	$video_list = $CI->m_video->get_ondemand_list($CI->session->userdata('site_config')->project_id, $speciality, $country);
	$video_id = $CI->input->get('video', true);
	$video_page_url = project_config('video_page_url') ? project_config('video_page_url') : 'offline';
	if (!isset($video_id) || empty($video_id) || $video_id == null) {
		$video_id = $CI->session->userdata('video_id');
	}
	$result_html = '<ul>';
	if (count($video_list)) {
		$i = 0;
		foreach ($video_list as $video) {
			$activeClass = '';
			if (($video->id == $video_id) || (!$video_id && $i = 0)) {
				$activeClass = 'active';
			}

			$result_html .= '<li class="' . $activeClass . ' video_' . $video->id . '">';
			$result_html .= '<figure>';
			$result_html .= '<a href="/' . $video_page_url . '?video=' . $video->id . '">';

			if ($video->img_url) {
				$result_html .= '<img src="/assets/static/' . $CI->session->userdata('site_config')->url . '/ondemand_video/' . $video->img_url . '" alt="" width="54" height="44">';
			} else {
				$result_html .= '<img src="/assets/common/general/images/video_thumbnail_default.png" alt="">';
			}

			$result_html .= '</a>';
			$result_html .= '</figure>';
			$result_html .= '<a href="/' . $video_page_url . '?video=' . $video->id . '">' . $video->title . ' <br/> <span class="doctor_name_list">' . $video->name . '</span></a>';
			$result_html .= '</li>';
			$i++;
		}
	}
	$result_html .= '</ul>';
	echo $result_html;
}

function getOndemandArchiveVideoList()
{
	$CI = get_instance();
	$CI->load->model('M_video', 'm_video');
	$speciality = $CI->session->userdata('site_user')->speciality;
	$country = $CI->session->userdata('site_user')->country;
	$video_list = $CI->m_video->get_ondemand_archive_list($CI->session->userdata('site_config')->project_id, $speciality, $country);
	$video_id = $CI->input->get('video', true);
	$video_page_url = project_config('video_page_url') ? project_config('video_page_url') : 'offline';
	$result_html = '<ul>';
	if (count($video_list)) {
		$i = 0;
		foreach ($video_list as $video) {
			$activeClass = '';
			if (($video->id == $video_id) || ($video_id && $i = 0)) {
				$activeClass = 'active';
			}

			$result_html .= '<li class="' . $activeClass . ' video_' . $video->id . '">';
			$result_html .= '<figure>';
			$result_html .= '<a href="/' . $video_page_url . '?video=' . $video->id . '">';

			if ($video->img_url) {
				$result_html .= '<img src="/assets/static/' . $CI->session->userdata('site_config')->url . '/ondemand_video/' . $video->img_url . '" alt="" width="54" height="44">';
			} else {
				$result_html .= '<img src="/assets/common/general/images/video_thumbnail_default.png" alt="">';
			}

			$result_html .= '</a>';
			$result_html .= '</figure>';
			$result_html .= '<a href="/' . $video_page_url . '?video=' . $video->id . '">' . $video->title . ' <br/> <span class="doctor_name_list">' . $video->name . '</span></a>';
			$result_html .= '</li>';
			$i++;
		}
	}
	$result_html .= '</ul>';
	echo $result_html;
}

function getOfflineVideo()
{
	$CI = get_instance();
	$CI->load->model('M_video', 'm_video');
	$project_id = $CI->session->userdata('site_config')->project_id;
	$video_id = $CI->input->get('video', true);

	$video = $CI->m_video->get_video($project_id, $video_id, strtolower($CI->agent->platform));

	if (is_null($video)) {
		$video = (object) [
			'video' => '<img src="/assets/common/general/images/video_not_found.png" class="img-responsive" />',
			'title' => '-', //$CI->lang->line('video_not_found'),
			'name' => '-'
		];
		$return_html = '';
	} else {
		$return_html = <<<HTML
        <div class="embed-responsive embed-responsive-16by9" data-id="{$video->id}">
            {$video->video}
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 video_title_desc">
            <div class="video_title">{$video->title}</div>
            <div class="video_dr">{$video->name}</div>
        </div>
HTML;
	}

	echo $return_html;
}

function getOndemandVideo()
{
	$CI = get_instance();
	$CI->load->model('M_video', 'm_video');
	$project_id = $CI->session->userdata('site_config')->project_id;
	$speciality = isset($CI->session->userdata('site_user')->speciality) ? $CI->session->userdata('site_user')->speciality : null;
	$country = $CI->session->userdata('site_user')->country;
	$video_id = $CI->input->get('video', true);
	if (!isset($video_id) || empty($video_id) || $video_id == null) {
		$video_id = $CI->session->userdata('video_id');
	}
	$video = $CI->m_video->get_ondemand_video($project_id, $video_id, $speciality, $country, strtolower($CI->agent->platform));

	if (is_null($video)) {
		$video = (object) [
			'video' => '<img src="/assets/common/general/images/video_not_found.png" class="img-responsive" />',
			'title' => '-', //$CI->lang->line('video_not_found'),
			'name' => '-'
		];
		$return_html = '';
	} else {
		$return_html = <<<HTML
        <div class="embed-responsive embed-responsive-16by9" data-id="{$video->id}">
            {$video->video}
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 video_title_desc">
            <div class="video_title">{$video->title}</div>
            <div class="speaker">
                        <div class="flex">
                            <div>
                                <img class="speaker__image" src="/file/images/{$video->speaker_img}" alt="">
                            </div>
                            <div class="speaker__information">
                                <div class="speaker__name">
                                    {$video->name} </div>
                                <div class="speaker__subtitle">
                                    {$video->speaker_affiliation} </div>
                                <a href="" class="speaker__more" data-toggle="modal" data-target="#modalProfileDetail">See Bio</a>
                            </div>
                        </div>
                    </div>
        </div>
HTML;
	}

	echo $return_html;
}

function go_prev()
{
	$CI = get_instance();
	$uri = $CI->uri->segment(1);
	$project_id = $CI->session->userdata('site_config')->project_id;
	if ($uri) {
		$page = $CI->m_router->get_page($project_id, $uri);

		if ($page) {
			redirect($page->prev);
		}
	}
}

function go_next()
{
	$CI = get_instance();
	$uri = $CI->uri->segment(1);
	$project_id = $CI->session->userdata('site_config')->project_id;
	if ($uri) {
		$page = $CI->m_router->get_page($project_id, $uri);

		if ($page) {
			redirect($page->next);
		}
	}
}

function gotoPage($condition, $goUrl)
{
	return false;

	if ($condition) {
		$CI = get_instance();
		$current_uri = $CI->uri->segment(1);
		$pages = $CI->session->userdata('site_pages'); //bu artık yok

		if ($goUrl == NEXT_PAGE) {
			foreach ($pages as $page) {
				if ($page->url == $current_uri) {
					if ($page->next) {
						$goUrl = $page->next;
						break;
					}
				}
			}
		} elseif ($goUrl == PREV_PAGE) {
			foreach ($pages as $page) {
				if ($page->url == $current_uri) {
					if ($page->prev) {
						$goUrl = $page->prev;
						break;
					}
				}
			}
		}
		redirect('/' . $goUrl);
	}
}

function siteLanguagesDropdown()
{
	$CI = get_instance();

	if (!isset($CI->session->userdata('site_config')->site_languages)) {
		return '';
	}

	$site_languages = $CI->session->userdata('site_config')->site_languages;
	if (!$site_languages) {
		return '';
	}

	//#curent language
	$current_lang = $CI->input->cookie('site_lang', true) ? $CI->input->cookie('site_lang', true) : $CI->session->userdata('site_config')->default_lang;
	$html = '<select name="site_languages" id="site_languages">';

	foreach ($site_languages as $s) {
		$selected = '';
		if ($current_lang == $s->id) {
			$selected = ' selected';
		}

		$html .= '<option value="' . $s->id . '"' . $selected . '>' . $s->local_name . '</option>';
	}
	$html .= '</select>';

	echo $html;
}

function siteLanguagesList()
{
	$CI = get_instance();

	if (!isset($CI->session->userdata('site_config')->site_languages)) {
		return '';
	}

	$site_languages = $CI->session->userdata('site_config')->site_languages;
	if (!$site_languages) {
		return '';
	}

	//#curent language
	$current_lang = $CI->input->cookie('site_lang', true) ? $CI->input->cookie('site_lang', true) : $CI->session->userdata('site_config')->default_lang;
	$html = '';

	foreach ($site_languages as $s) {
		$selected = '';
		if ($current_lang == $s->id) {
			$selected = ' selected';
		}

		$html .= ' <a href="javascript:;" onclick="change_language(' . $s->id . ');">' . $s->local_name . '</a>';
	}

	echo $html;
}

function contact_support_lightbox_html()
{
	return '<!-- Modal content-->
                  <div class="modal-content">
                        <div class="modal-header">
                             <button type="button" class="close" data-dismiss="modal" style="font-size: 40px;">&times;</button>
                             <h2 class="customText1 txt_center">{contact_title}</h2>
                             <p class="formText customText1 txt_center">
                                {contact_subtitle}
                             </p>
                        </div>
                        <div class="modal-body">
                             <div class="formLogin">
                                  <div id="preloader" class="dn"><div id="status" style="margin-top: -129px;">&nbsp;</div></div>
                                  <h2 class="customText1 txt_center formResult dn"></h2>
                                  <form id="contact_form">
                                        <p>
                                             <span class="lgnInputText">{fullname} <small class="dn">{error_empty}</small> </span>
                                             <input name="fullname" type="text" placeholder="" value="">
                                        </p>
                                        <p>
                                             <span class="lgnInputText">{email} <small class="dn">{error_empty}</small> <small class="dn">{error_email}</small></span>
                                             <input name="email" type="text" placeholder="" value="">
                                        </p>
                                        <p>
                                             <span class="lgnInputText">{phone} <small class="dn">{error_empty}</small> </span>
                                             <input name="phone" type="number" class="numeric" placeholder="" maxlength="15">
                                        </p>
                                        <p>
                                             <span class="lgnInputText">{contact_message} <small class="dn">{error_empty}</small> </span>
                                             <textarea name="message" placeholder=""></textarea>
                                        </p>
                                        <div class="btnDefault">
                                             <a id="submit_contact_form" class="customBg1">{send}</a>
                                        </div>
                                  </form>
                             </div>
                        </div>
                  </div> ';
}

function backup_embed_buttons()
{
	return backupEmbedDropdown();

	$CI = get_instance();

	//#eğer site offline modda ise offline sayfasına yönlendir
	if (!$CI->session->has_userdata('site_config')) {
		return '';
	}

	$project_id = $CI->session->userdata('site_config')->project_id;

	//#load embed model
	$CI->load->model('M_embed', 'm_embed');

	//#get embed
	$embed = $CI->m_embed->get_embed($project_id, strtolower($CI->agent->platform), $CI->session->userdata('site_config')->stream_lang);

	$html = '';
	if (count($embed)) {
		$db_column = 'main';
		if ($CI->session->has_userdata('embed')) {
			$db_column = $CI->session->userdata('embed');
		}

		//HTML
		if (!empty($embed->backup1) || !empty($embed->backup2)) {
			$active_class = $db_column == 'main' ? 'active' : 'opacity60';
			$html .= '<a href="javascript:;" class="' . $active_class . '" onclick="change_embed(\'main\');"><img src="/assets/common/general/images/embed_main.jpg" alt="" /></a>';
		}

		if (!empty($embed->backup1)) {
			$active_class = $db_column == 'backup1' ? 'active' : 'opacity60';
			$html .= '<a href="javascript:;" class="' . $active_class . '" onclick="change_embed(\'backup1\');"><img src="/assets/common/general/images/embed_backup1.jpg" alt="" /></a>';
		}

		if (!empty($embed->backup2)) {
			$active_class = $db_column == 'backup2' ? 'active' : 'opacity60';
			$html .= '<a href="javascript:;" class="' . $active_class . '" onclick="change_embed(\'backup2\');"><img src="/assets/common/general/images/embed_backup2.jpg" alt="" /></a>';
		}
	}

	echo $html;
}

function backupEmbedDropdown()
{
	$CI = get_instance();

	if (!$CI->session->has_userdata('site_config')) {
		return '';
	}

	$CI->load->model('M_embed', 'm_embed');

	$project_id = $CI->session->userdata('site_config')->project_id;

	$embed = $CI->m_embed->get_embed(
		$project_id,
		strtolower($CI->agent->platform),
		$CI->session->userdata('site_config')->stream_lang
	);

	$html = '';
	if ($embed) {
		$selected = 'main';
		if ($CI->session->has_userdata('embed')) {
			$selected = $CI->session->userdata('embed');
		}

		if (!empty($embed->backup1) || !empty($embed->backup2)) {
			$html .=
				'<span>{stream_backup_label} </span>' .
				'<select class="alternateChannels">' .
				'<option value="main" ' . ($selected == 'main' ? 'selected' : '') . '>{stream_backup_main}</option>';
		}

		if (!empty($embed->backup1)) {
			$html .= '<option value="backup1" ' . ($selected == 'backup1' ? 'selected' : '') . '>{stream_backup_1}</option>';
		}

		if (!empty($embed->backup2)) {
			$html .= '<option value="backup2" ' . ($selected == 'backup2' ? 'selected' : '') . '>{stream_backup_2}</option>';
		}

		if (!empty($embed->backup1) || !empty($embed->backup2)) {
			$html .= '</select>';
		}
	}

	echo $html;
}

function hideChat()
{
	echo '<style type="text/css">
            .liveResizer1 a{ display: none; }
            .currentLocation .chatLocation .liveChat{ display: none !important; }
        </style>
        <script type="text/javascript">
            hideChat = true;
        </script>';
}

function get_custom_data()
{
	$CI = get_instance();
	$project_id = $CI->session->userdata('site_config')->project_id;
	$user = $CI->session->userdata('site_user');
	$query = $CI->db->select('*')->from('custom_data')->where(['project_id' => $project_id, 'user_id' => $user->reg_id])->get();
	if ($query) {
		return $query->row();
	}
}


function ondemand_getby_project($id, $speciality, $w = 'all')
{
	$CI = get_instance();
	$now = date('Y-m-d H:i:s');
	if ($w == 'all') {
		$query = $CI->db->select('*')->from('ondemandvideo');
	} elseif ($w == 'live') {
		$query = $CI->db->select('*')->from('ondemandvideo')->where('expired_date >=', $now)->where('stream_date <=', $now);
	} elseif ($w == 'archive') {
		$query = $CI->db->select('*')->from('ondemandvideo');
	} else {
		$query = $CI->db->select('*')->from('ondemandvideo');
	}
	$query->where(['project_id' => $id])->like('speciality', $speciality)->get();


	if ($query) {
		return $query->result();
	}
}

function ondemand_getby_id($id)
{
	$CI = get_instance();
	$query = $CI->db->select('*')->from('ondemandvideo')->where(['id' => $id])->get();
	if ($query) {
		return $query->row();
	}
}

function ondemand_getby_category($id)
{
	$CI = get_instance();
	$query = $CI->db->select('*')->from('ondemandvideo')->where(['category_id' => $id])->get();
	if ($query) {
		return $query->result();
	}
}

function subdomainDropdown($placeHolder = '{select}')
{
	$subdomains = project_config('subdomains');
	$subdomains = explode(',', $subdomains);
	$subdomains = array_map('trim', $subdomains);


	$html = '';
	foreach ($subdomains as $key => $s) {
		$html .= '<option data-id="' . ($key + 1) . '" value="' . $s . '">{' . $s . '_title}</option>';
	}

	echo $html;
}

function getSubdomains()
{
	$subdomains = project_config('subdomains');
	$subdomains = explode(',', $subdomains);
	$subdomains = array_map('trim', $subdomains);

	return $subdomains;
}

function countryDropdown($placeHolder = '{select}', $pid = -1)
{
	$CI = get_instance();
	if ($pid == -1) {
		$pid = $CI->session->userdata('site_config')->project_id;
	}
	$CI->db->select('id,name,value');
	$CI->db->from('countries');
	$CI->db->where('project_id', $pid);
	$CI->db->order_by('name', 'asc');
	$countries = $CI->db->get()->result();

	if (count($countries) < 1 && $pid != 0) {
		return countryDropdown($placeHolder, 0);
	}


	$html = '<option data-id="0" class="dn" value="">' . $placeHolder . '</option>';
	foreach ($countries as $c) {
		$html .= '<option data-id="' . $c->id . '" value="' . $c->value . '">' . $c->name . '</option>';
	}

	echo $html;
}

function cityDropdown($placeHolder = '{select}', $pid = 0)
{
	$CI = get_instance();
	$CI->db->select('*');
	$CI->db->from('cities');
	$CI->db->order_by('name', 'asc');
	$cities = $CI->db->get()->result();


	$html = '<option data-id="0" data-country="0" data-phone="0" class="dn" value="">' . $placeHolder . '</option>';
	foreach ($cities as $c) {
		$html .= '<option data-id="' . $c->id . '" data-country="' . $c->country_id . '" data-phone="' . $c->phone_code . '" value="' . $c->name . '">' . $c->name . '</option>';
	}

	echo $html;
}

function cityDropdownWithSelected($selected, $pid = 0)
{
	$CI = get_instance();
	$CI->db->select('*');
	$CI->db->from('cities');
	$CI->db->order_by('name', 'asc');
	$cities = $CI->db->get()->result();


	$html = '';
	foreach ($cities as $c) {
		if (in_array($c->name, $selected)) {
			$html .= '<option data-id="' . $c->id . '" data-country="' . $c->country_id . '" data-phone="' . $c->phone_code . '" value="' . $c->name . '" selected="selected">' . $c->name . '</option>';
		} else {
			$html .= '<option data-id="' . $c->id . '" data-country="' . $c->country_id . '" data-phone="' . $c->phone_code . '" value="' . $c->name . '">' . $c->name . '</option>';
		}
	}

	echo $html;
}

function countryDropdownWithSelected($selected, $pid = 0)
{
	$CI = get_instance();
	$CI->db->select('id,name,value');
	$CI->db->from('countries');
	$CI->db->where('project_id', $pid);
	$CI->db->order_by('name', 'asc');
	$countries = $CI->db->get()->result();

	$html = '';
	foreach ($countries as $c) {
		if (in_array($c->value, $selected)) {
			$html .= '<option data-id="' . $c->id . '" value="' . $c->value . '" selected="selected">' . $c->name . '</option>';
		} else {
			$html .= '<option data-id="' . $c->id . '" value="' . $c->value . '">' . $c->name . '</option>';
		}
	}
	echo $html;
}


function specialityDropdown($placeHolder = '{select}', $pid = -1)
{
	$CI = get_instance();
	if ($pid == -1) {
		$pid = $CI->session->userdata('site_config')->project_id;
	}
	$CI->db->select('*');
	$CI->db->where('project_id', $pid);
	$CI->db->from('specialities');
	$CI->db->order_by('display_name', 'asc');
	$specialities = $CI->db->get()->result();

	if (count($specialities) < 1 && $pid != 0) {
		return specialityDropdown($placeHolder, 0);
	}

	$html = '<option data-id="0" data-name="0" class="dn" value="">' . $placeHolder . '</option>';
	foreach ($specialities as $s) {
		$html .= '<option data-id="' . $s->id . '" data-name="' . $s->name . '" value="' . $s->value . '">' . $s->display_name . '</option>';
	}
	$html .= '<option value="other">{other}</option>';
	echo $html;
}

function specialityDropdownWithSelected($selected, $pid = 0)
{
	$CI = get_instance();

	$CI->db->select('*');
	$CI->db->where('project_id', $pid);
	$CI->db->from('specialities');
	$CI->db->order_by('display_name', 'asc');
	$specialities = $CI->db->get()->result();

	$count = count($specialities);
	if ($count < 1) {
		$CI->db->select('*');
		$CI->db->where('project_id', 0);
		$CI->db->from('specialities');
		$CI->db->order_by('display_name', 'asc');
		$specialities = $CI->db->get()->result();
	}

	$CI->db->select('*');
	$CI->db->from('lang_key');
	$keys = $CI->db->get()->result();

	foreach ($keys as $k) {
		$words[$k->key] = $k->default_value;
	}
	$html = "";
	foreach ($specialities as $s) {
		if (in_array($s->name, $selected)) {
			$html .= '<option value="' . $s->name . '" selected="selected">' . $words[$s->name] . '</option>';
		} else {
			$html .= '<option value="' . $s->name . '">' . @$words[$s->name] . '</option>';
		}
	}
	$html .= '<option value="other">' . $words['other'] . '</option>';
	echo $html;
}

/**
 * Pfizer'e özel /info sayfasına sadece 1  kişi ile girdiğinize emin misiniz uyarısı
 */
function ask_pfizer_more_than_one_people()
{
	$CI = get_instance();

	if (!$CI->session->has_userdata('site_user')) {
		return '';
	}

	$email = $CI->session->userdata('site_user')->email;
	$is_pfizer_email = strpos($email, '@pfizer') !== false;

	//pfizer maili değilse aşağıdaki işlemin çalışmasına gerek yok !
	if (!$is_pfizer_email) {
		return '';
	}

	echo <<<HTML
        <link rel="stylesheet" href="/assets/static/global/sweetalert2/sweetalert2.min.css" />
        <script type="text/javascript" src="/assets/static/global/sweetalert2/es6-promise.auto.min.js"></script>
        <script type="text/javascript" src="/assets/static/global/sweetalert2/sweetalert2.min.js"></script>

        <script type="text/javascript">
            var AskedHowManyPeople = false;
            function CheckAudiencesNumber() {

                if( !AskedHowManyPeople && $(".audienceNumberInput").val() == "1" && $(".person_name_input").val() == "" ){
                    swal({
                        title: "{are_you_sure}",
                        html: "{change_audience_number}",
                        type: "warning",
                        showCancelButton: true,
                        cancelButtonColor: "#00ab67",
                        confirmButtonColor: "#f8bb86",
                        cancelButtonText: "{take_me_back}",
                        confirmButtonText: "{only_me}",
                        //closeOnConfirm: false
                    })
                    .then(function () {

                        get_audiences();

                    },function (dismiss) {

                        if (dismiss === "cancel")
                            AskedHowManyPeople = true;
                    })
                    .catch(swal.noop);

                }else{
                    get_audiences();
                }

            } // CheckAudiencesNumber func.

            $(function(){
                $(".connect_to_stream_btn").attr("onclick", "CheckAudiencesNumber();");
            });

        </script>
HTML;
}

/**
 * Returns user session data
 */
function user_info($key = null, $default = null)
{
	$CI = get_instance();
	$site_user = $CI->session->userdata('site_user');

	if (!$site_user) {
		return $default;
	}

	if ($key) {
		if (property_exists($site_user, $key)) {
			return $site_user->{$key};
		} else {
			return $default;
		}
	}
	return $site_user;
}

/**
 * Alias for user_info func.
 */
function site_user($key = null, $default = null)
{
	return user_info($key, $default);
}

/**
 * Returns site config session data
 */
function site_config($key = null, $default = null)
{
	$CI = get_instance();
	$site_config = $CI->session->userdata('site_config');

	if (!$site_config) {
		return $default;
	}

	if ($key) {
		if (property_exists($site_config, $key)) {
			return $site_config->{$key};
		} else {
			return $default;
		}
	}

	return $site_config;
}

/**
 * Returns site config from database
 */
function project_config($key)
{
	$CI = get_instance();
	$project_id = $CI->session->userdata('site_config')->project_id;
	return $CI->m_router->get_config($project_id, $key);
}

function project_configs(array $keys)
{
	if (empty($keys)) {
		return null;
	}
	$CI = get_instance();
	$project_id = $CI->session->userdata('site_config')->project_id;
	$data = $CI->m_router->get_configs($project_id, $keys);
	foreach ($keys as $key) {
		$search = array_search($key, array_column($data, 'key'));
		if ($search === false) {
			$data[] = (object) [
				'key' => $key,
				'value' => null
			];
		}
	}
	return $data;
}

function project_config_default($key, $default_value = "")
{
	$CI = get_instance();
	$project_id = $CI->session->userdata('site_config')->project_id;
	$result = $CI->m_router->get_config($project_id, $key);
	return !empty($result) ? $result : $default_value;
}

function set_config($key, $value)
{
	$CI = get_instance();
	$project_id = $CI->session->userdata('site_config')->project_id;
	return $CI->m_router->set_config($project_id, $key, $value);
}

function update_config($key, $value)
{
	$CI = get_instance();
	$project_id = $CI->session->userdata('site_config')->project_id;
	return $CI->m_router->update_config($project_id, $key, $value);
}

function set_or_update_config($key, $value)
{
	$CI = get_instance();
	$project_id = $CI->session->userdata('site_config')->project_id;
	return $CI->m_router->set_or_update_config($project_id, $key, $value);
}

function delete_config($key)
{
	$CI = get_instance();
	$project_id = $CI->session->userdata('site_config')->project_id;
	return $CI->m_router->delete_config($project_id, $key);
}

function vipanel_project_config($key, $default_value = "")
{
	$CI = get_instance();
	$project_id = $CI->session->userdata('site_config')->project_id;
	$result = $CI->m_router->get_vipanel_config($project_id, $key);
	return !empty($result) ? $result : $default_value;
}

function vipanel_set_config($key, $value)
{
	$CI = get_instance();
	$project_id = $CI->session->userdata('site_config')->project_id;
	return $CI->m_router->set_vipanel_config($project_id, $key, $value);
}

function vipanel_update_config($key, $value)
{
	$CI = get_instance();
	$project_id = $CI->session->userdata('site_config')->project_id;
	return $CI->m_router->update_vipanel_config($project_id, $key, $value);
}

function set_or_update_vipanel_config($key, $value)
{
	if (vipanel_project_config($key) || vipanel_project_config($key) === "0") {
		vipanel_update_config($key, $value);
	} else {
		vipanel_set_config($key, $value);
	}
}

function vipanel_delete_config($key)
{
	$CI = get_instance();
	$project_id = $CI->session->userdata('site_config')->project_id;
	return $CI->m_router->delete_vipanel_config($project_id, $key);
}

function get_project_by_url($url){
	$CI = get_instance();
	return $CI->db->from('project')
		->where('url', $url)
		->get()
		->row();
}

function check_vipanel_decline()
{
	$CI = get_instance();

	$email = $CI->session->userdata('site_user')->email;
	$project_id = $CI->session->userdata('site_config')->project_id;

	return $CI->m_router->check_vipanel_decline($project_id, $email);
}

/**
 * Return logged users country
 *
 * @return string
 */
function user_country()
{
	return site_user('country', '');
}

function user_country_is($country)
{
	return strtolower(site_user('country', '')) == strtolower($country);
}

function user_country_in($arr = [])
{
	return in_array(site_user('country'), $arr);
}

function set_stream_language($lang)
{
	$CI = get_instance();

	$CI->session->userdata('site_config')->stream_lang = $lang;
}

function get_stream_language()
{
	return site_config('stream_lang');
}

/**
 * Kullanılmıyorsa kaldırılmalı (ya da get_ip_country_lang şeklinde adı günellenmeli !)
 */
function get_ip_country()
{
	$ip = $_SERVER['REMOTE_ADDR'];
	$CI = get_instance();
	$CI->load->model('M_lang', 'm_lang');
	return $CI->m_lang->ip_to_iso($ip)->lang;
}

function get_country_by_ip()
{
	$ip = $_SERVER['REMOTE_ADDR'];
	$CI = get_instance();
	$CI->load->model('M_lang', 'm_lang');
	return $CI->m_lang->ip_to_iso($ip)->name;
}

function get_ip_country_iso()
{
	$ip = $_SERVER['REMOTE_ADDR'];
	$CI = get_instance();
	$CI->load->model('M_lang', 'm_lang');
	return $CI->m_lang->ip_to_iso($ip)->iso;
}

/**
 * Siteye ait static klasöründen dosya okur
 */
function readFileFromStatic($file_name)
{
	echo file_get_contents(
		'/var/www/vhosts/viengine_v2/website/assets/static/' . site_config('url') . str_start($file_name, '/')
	);
}

/**
 * (FROM LARAVEL !)
 * Begin a string with a single instance of a given value.
 *
 * @param string $value
 * @param string $prefix
 * @return string
 */
function str_start($value, $prefix)
{
	$quoted = preg_quote($prefix, '/');

	return $prefix . preg_replace('/^(?:' . $quoted . ')+/u', '', $value);
}

/**
 * Chat'te mesaj gönderildikten sonra kullanıcıya notification gösterme işlemi !
 */
function chat_thank_you_after_mesage($message = '{chat_thanks_after_mesage}')
{
	$chat_thanks_message = project_config('chat_thanks_message');
	if ($chat_thanks_message === false || $chat_thanks_message == 1) { // not isset or equal 1
		echo '
        <script type="text/javascript">
            function HookAfterMessageSend(){
                $(\'.liveChat form input[type="text"]\').notify("' . $message . '", "success");
            }
        </script>';
	}
}

/**
 * Chat'te ismimi gizle checkbox'ı
 */
function chat_hide_name_option($text = '{chat_hide_name}', $desc = '{chat_hide_name_description}')
{
	if (project_config('chat_hide_name') == 1) {
		//
		$html = '<div class="checkbox">';
		$html .= '
            <label>
                <input type="checkbox" value="1" name="noname">
                <span style="font-size: small">' . $text . '</span>&nbsp;
            </label>';
		if ($desc) {
			$html .= '<span class="glyphicon glyphicon-question-sign" data-toggle="tooltip" title="' . $desc . '"></span>';
		}
		$html .= '</div>';

		$html .= '
        <style type="text/css">
            @media screen and (min-width: 1200px){
                .chatHor { height: 173px !important; }
            }
            @media screen and (min-width: 992px) and (max-width: 1200px){
                .chatHor { height: 164px !important; }
            }
            @media screen and (min-width: 768px) and (max-width: 992px){
                .chatHor { height: 93px !important; }
            }
        </style>';
		echo $html;
	}
}

/**
 * Generate Certificate !
 *
 * @param string $start_date
 * @param string $end_date
 * @param bool $allow_multi_name
 * @param string $img_file
 * @param string $redirect_to
 * @param array $text_color
 *
 * @return string
 */
function generateCertificate(
	$start_date = '2018-01-01 00:00:00',
	$end_date = '2036-01-01 00:00:00',
	$allow_multi_name = false,
	$img_file = null,
	$redirect_to = 'stream',
	$text_color = [9, 78, 111],
	$x = 1,
	$y = 68,
	$show_border = 0,
	$return_html = false,
	$custom_font = null,
	$custom_font_size = 14, //pt
	$frame = 'L',
	$isOutput = 1,
	$outputType = 'D',
	$logActive = true
) {
	$pages = project_config('certificate_pages') ? project_config('certificate_pages') : 1;
	$certificate_page = project_config('certificate_page') ? project_config('certificate_page') : 1;
	$certificate_text_align = project_config('certificate_text_align') ? project_config('certificate_text_align') : 'C';
	$certificate_x_start = project_config('certificate_x_start') ? project_config('certificate_x_start') : 200;
	$certificate_line_height = project_config('certificate_line_height') ? project_config('certificate_line_height') : 45;
	$certificate_line_height2 = project_config('certificate_line_height2') ? project_config('certificate_line_height2') : 65;
	$certificate_font_size2 = project_config('certificate_font_size2') ? project_config('certificate_font_size2') : 20;
	$certificate_font_size3 = project_config('certificate_font_size3') ? project_config('certificate_font_size3') : 20;

	$current_time = date('Y-m-d H:i:s');

	$can_process = ($current_time >= $start_date && $current_time <= $end_date);

	$CI = get_instance();

	// if ajax return json (allowed to run this func.)
	if ($CI->input->is_ajax_request()) {
		$CI->layout = false;

		echo json_encode([
			'success' => $can_process
		]);
		exit;
	}

	// if cannot process return to stream page
	if (!$can_process) {
		redirect(str_start($redirect_to, '/'));
		exit;
	}

	$static_path = ASSETPATH . 'static/' . site_config('url');

	$user_names = [user_info('fullname')];
	if (user_info('certificate_name')) {
		$user_names = [user_info('certificate_name')];
	}
	if ($allow_multi_name) {
		if ($_POST) {
			$user_names = $CI->input->post('names', true);
			if (!is_array($user_names) || !count($user_names)) {
				// post ile gelinmiş fakat değer girilmemiş ise !
				redirect($CI->uri->uri_string());
				exit;
			}
		} else {
			// $CI->layout = true;
			// return html page here
			// check if custom html exists
			$html_file = VIEWPATH . 'custom_html/certificate.php';
			if (file_exists($static_path . '/certificate.php')) {
				$html_file = $static_path . '/certificate.php';
			}

			$response = file_get_contents($html_file);

			if ($return_html) {
				return $response;
			}

			echo $response;
			return;
		}
	}

	$CI->layout = false;

	// https://tcpdf.org/docs/
	require APPPATH . 'third_party/tcpdf/tcpdf.php';

	// log user info !
	ini_set('error_log', $static_path . '/certificate/certificate.log');
	error_log(print_r(user_info(), true));
	if ($frame == 'P') {
		$pageSize = array(413, 584);
		$pdf = new TCPDF('P', "px", $pageSize, true, 'UTF-8', false);
		$image_default_height = 584;
		$image_width = project_config('certificate_image_width') ? project_config('certificate_image_width') : 413;
		$image_height = project_config('certificate_image_height') ? project_config('certificate_image_height') : $image_default_height;
	} else {
		$image_default_height = 210;
		$image_width = project_config('certificate_image_width') ? project_config('certificate_image_width') : 297;
		$image_height = project_config('certificate_image_height') ? project_config('certificate_image_height') : $image_default_height;
		$pdf = new TCPDF('L', PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
	}


	//PDF CONFIG
	$pdf->SetCreator(PDF_CREATOR);
	$pdf->SetAuthor('');
	$pdf->SetTitle('');
	$pdf->SetSubject('');
	$pdf->SetKeywords('');
	$pdf->SetHeaderMargin(0);
	$pdf->SetFooterMargin(0);
	$pdf->setPrintFooter(false);

	if (!$custom_font) {
		$pdf->addFont('parisiennei', '', 'parisiennei.php');
		$pdf->SetFont('parisienne', 'B', $custom_font_size ? $custom_font_size : 40);
	} else {
		$pdf->SetFont($custom_font, '', $custom_font_size, '', false);
	}

	// set text color
	$pdf->SetTextColor($text_color[0], $text_color[1], $text_color[2]);
	//PDF CONFIG

	// set background
	//TODO: country based?
	if (!$img_file) {
		$img_file = $static_path . '/certificate/bg.png';
	} else {
		$img_file = $static_path . str_start($img_file, '/');
	}
	if (project_config('certificate_img_language')) {
		$current_lang = $CI->input->cookie('site_lang', true) ? $CI->input->cookie('site_lang', true) : $CI->session->userdata('site_config')->default_lang;
		$img_file = $static_path . '/certificate/bg' . $current_lang . '.png';
	}
	if (user_info('certificate_img')) {
		$img_file = $static_path . '/certificate/' . user_info('certificate_img');
	}

	// Check if bg image exists !
	$can_add_image = file_exists($img_file);

	foreach ($user_names as $user_name) {
		$pdf->AddPage();
		$pdf->SetAutoPageBreak(false, 0);

		$pdf->setPageMark();
		$pdf->setY($y);
		$pdf->setX($x);
		// bg image
		if ($can_add_image) {
			for ($i = 0; $i < $pages; $i++) {
				if ($i != 0) {
					$pdf->AddPage();
				}
				$pdf->Image($img_file, 0, (-1 * $i * $image_default_height), $image_width, $image_height, '', '', '', false, 300, '', false, false, 0);
				if (($i + 1) == $certificate_page) {
					$pdf->Cell($certificate_x_start, 0, $user_name, $show_border, 0, $certificate_text_align);
					if (user_info('certificate_name2')) {
						if (!project_config('certificate_font2')) {
							$pdf->addFont('parisiennei', '', 'parisiennei.php');
							$pdf->SetFont('parisienne', 'B', $certificate_font_size2);
						} else {
							$pdf->SetFont(project_config('certificate_font2'), '', $certificate_font_size2, '', false);
						}
						$pdf->setX($x);
						$pdf->Cell($certificate_x_start, $certificate_line_height, user_info('certificate_name2'), $show_border, 0, $certificate_text_align);
					}
					if (user_info('certificate_name3')) {
						if (!project_config('certificate_font3')) {
							$pdf->addFont('parisiennei', '', 'parisiennei.php');
							$pdf->SetFont('parisienne', 'B', $certificate_font_size3);
						} else {
							$pdf->SetFont(project_config('certificate_font3'), '', $certificate_font_size3, '', false);
						}
						$pdf->setX($x);
						$pdf->Cell($certificate_x_start, $certificate_line_height2, user_info('certificate_name3'), $show_border, 0, $certificate_text_align);
					}
				}
			}
		}

		if ($logActive) {
			$certificate_data = [
				'log_id' => user_info('log_id'),
				'name' => $user_name,
				'certificate' => user_info('log_id') . '.pdf'
			];
			$CI->db->insert('certificates', $certificate_data);
		}
	}

	if (file_exists($static_path . '/certificate/output/')) {
		$pdfFileOutput = $pdf;
		$pdfFilePath = $static_path . '/certificate/output/' . user_info('log_id') . '.pdf';
		$pngFilePath = $static_path . '/certificate/output/' . user_info('log_id') . '.png';
		$pdfFileOutput->Output($pdfFilePath, 'F');
		exec("convert -density 300 -resize '30%' -quality 100 {$pdfFilePath} {$pngFilePath}");
	}
	if ($isOutput) {
		$pdf->Output('certificate.pdf', $outputType ?? 'D');
	}
}

/**
 * @param $class
 * @param $property
 * @param null $default
 * @return null
 */
function getProperty($class, $property, $default = null)
{
	if (property_exists($class, $property)) {
		return $class->{$property};
	}
	return $default;
}

function pollingJS($includeSocketJS = false)
{
	if ($includeSocketJS) {
		// echo '<script src="https://vistreamsocket.vistream.tv/socket.io/socket.io.js"></script>';
	}

	$pollingJS = 'static/global/polling/main.js';

	echo '<script src="/assets/' . $pollingJS . '?v=' . date('Ymd') . '.' . filetime(ASSETPATH . $pollingJS) . '>"></script>';
}

function httpAuth($username, $password, $message = 'Access Denied.')
{
	$CI = get_instance();

	if (($CI->input->server('PHP_AUTH_USER') != $username) || ($CI->input->server('PHP_AUTH_PW') != $password)) {
		header('HTTP/1.1 401 Unauthorized');
		header('WWW-Authenticate: Basic realm="Password For Stream"');
		exit($message);
	}
}

function getUserCountryFromIp($ip = null)
{
	if ($ip === null) {
		$CI = get_instance();
		$ip = $CI->input->ip_address();
	}

	$ipInfo = simplexml_load_file('http://www.geoplugin.net/xml.gp?ip=' . $ip);

	if ($ipInfo) {
		return $ipInfo->geoplugin_countryName;
	}

	return null;
}

function getQuizOptions($question_id, $required, $type, $order)
{
	$CI = get_instance();
	$options = $CI->db->select('*')
		->from('quiz_options')
		->where([
			'question_id' => $question_id
		])
		->order_by('quiz_options.order', 'asc')
		->get()
		->result();
	$html = "";
	if ($required) {
		$req = "required";
	} else {
		$req = "";
	}
	if ($type == "Selectbox") {
		$html .= '<select name="answers[' . $question_id . '][]" class="form-control col-lg-4 col-md-6 col-sm-12 col-xs-12 " id="question' . $question_id . '" ' . $req . ' >';
		$html .= '<option disabled selected value>{please_select_answer}</option>';
		foreach ($options as $option) {
			$html .= '<option value="' . $option->order . '">' . $option->option . '</option>';
		}
		$html .= '</select>';
	} elseif ($type == "Radio") {
		foreach ($options as $key => $option) {
			$html .= '<div class="survey-radios-group">
                              <label><input type="radio" name="answers[' . $question_id . '][]" value="' . ($key + 1) . '"  ' . $req . '> ' . $option->option . '</label>
                            </div>';
		}
	}
	return $html;
}

function getQuizOption($question_id, $order)
{
	$CI = get_instance();
	$option = $CI->db->select('*')
		->from('quiz_options')
		->where([
			'question_id' => $question_id,
			'order' => $order,
		])
		->get()
		->row();
	if ($option) {
		return $option->option;
	} else {
		return NULL;
	}
}

function getQuizForm($quiz_no = 1)
{
	$CI = get_instance();
	$project_id = $CI->session->userdata('site_config')->project_id;
	$log_id = $CI->session->userdata('site_user')->log_id;
	$quiz_question_no = 0;
	$questions = $CI->db
		->select('*')
		->from('quiz_questions')
		->where([
			'project_id' => $project_id,
			'quiz_no' => $quiz_no
		])
		->where('quiz_questions.id NOT IN (select question_id from quiz_answers where user_id=' . $log_id . ')', NULL, FALSE)
		->order_by('quiz_questions.order', 'asc')
		->get()
		->result();
	$html = "";
	$html .= '<form role="form" id="quiz" method="post">';
	$html .= '<div id="quizSuccessMessage" class="dn" >
						<h3>{quiz_thank_you}</h3>
					</div>
					<div id="quizFailMessage" class="dn">
						<h3 class="text-danger">{quiz_error}</h3>
					</div>';
	if (count($questions) > 0) {
		$html .= '<input type="hidden" name="quiz_no" value="' . $quiz_no . '">';
		foreach ($questions as $key => $question) {
			if ($question->type == "Text") {
				$html .= '<label class="quiz_question_text">' . $question->question . '</label>';
			} else {
				$quiz_question_no = $quiz_question_no + 1;
				$html .= '<div class="form-group">
                        <label><span class="quiz_question_no">' . $quiz_question_no . '.</span> ' . $question->question . '</label>' . getQuizOptions($question->id, $question->required, $question->type, $key + 1) . '
                    </div>';
			}
		}
		$html .= '<button type="submit" class="btn-next btn-animate mb-3 get_quiz_result_btn">
                                {save}
                            </button>
                        </div>';
		$html .= '</form>';
	} else {
		$corrects = $CI->db
			->select('quiz_questions.id, quiz_correct_answers.option_order, answer')
			->where('quiz_questions.project_id', $project_id)
			->where('quiz_questions.quiz_no', $quiz_no)
			->where('quiz_answers.user_id', $log_id)
			->from('quiz_questions')
			->join('quiz_correct_answers', 'quiz_correct_answers.question_id = quiz_questions.id', 'LEFT')
			->join('quiz_answers', 'quiz_answers.question_id = quiz_questions.id', 'LEFT')
			->group_by(array("quiz_questions.id", "user_id"))->get()->result();

		$count_corrects = 0;
		foreach ($corrects as $correct) {
			if ($correct->answer == $correct->option_order) {
				$count_corrects += 1;
			}
		}
		$html .= '</form>';
	}
	echo $html;
}

function getQuizResult($quiz_no = 1)
{
	$CI = get_instance();
	$project_id = $CI->session->userdata('site_config')->project_id;
	$log_id = $CI->session->userdata('site_user')->log_id;
	$html = "";
	$count_corrects = 0;
	$count_totals = 0;
	$questions = $CI->db
		->select('quiz_questions.id, quiz_questions.type, quiz_questions.question, quiz_correct_answers.option_order, answer')
		->where('quiz_questions.project_id', $project_id)
		->where('quiz_questions.quiz_no', $quiz_no)
		->group_start()
		->where('quiz_answers.user_id', $log_id)
		->or_where('quiz_answers.user_id IS NULL', null, false)
		->group_end()
		->from('quiz_questions')
		->join('quiz_correct_answers', 'quiz_correct_answers.question_id = quiz_questions.id', 'LEFT')
		->join('quiz_answers', 'quiz_answers.question_id = quiz_questions.id', 'LEFT')
		->group_by(array("quiz_questions.id", "user_id"))
		->order_by('quiz_questions.order', 'asc')
		->get()->result();

	$quiz_question_no = 0;
	foreach ($questions as $key => $question) {
		if ($question->type == "Text") {
			$html .= '<label class="quiz_question_text">' . $question->question . '</label>';
		} else {
			$quiz_question_no = $quiz_question_no + 1;
			$html .= '<div class="form-group quiz_question">
                        <label><span class="quiz_question_no">' . $quiz_question_no . '.</span> ' . $question->question . '</label>
                    </div>';
		}
		if ($question->answer == $question->option_order) {
			$count_corrects += 1;
			$html .= '<div style="color:green">' . getQuizOption($question->id, $question->option_order) . '</div>';
		} else {
			$html .= '<div style="color:red">' . getQuizOption($question->id, $question->answer) . '</div>';
			$html .= '<div style="color:green">' . getQuizOption($question->id, $question->option_order) . '</div>';
		}
		$count_totals++;
	}

	$html .= '<div><strong class="correct_questions">' . $count_corrects . '</strong> {out_of} <strong class="total_questions">' . $count_totals . '</strong> {questions} {answered_correctly}</div>';

	return $html;
}

function isQuizAnswered($quiz_no = 1)
{
	$CI = get_instance();
	$project_id = $CI->session->userdata('site_config')->project_id;
	$log_id = $CI->session->userdata('site_user')->log_id;

	$answered = $CI->db
		->select('quiz_questions.id')
		->where('quiz_questions.project_id', $project_id)
		->where('quiz_questions.quiz_no', $quiz_no)
		->where('quiz_answers.user_id', $log_id)
		->from('quiz_questions')
		->join('quiz_answers', 'quiz_answers.question_id = quiz_questions.id', 'LEFT')
		->get()->row();

	return $answered ? TRUE : FALSE;
}

function url_make($str)
{
	$before = array('ı', 'ğ', 'ü', 'ş', 'ö', 'ç', 'İ', 'Ğ', 'Ü', 'Ö', 'Ç'); // , '\'', '""'
	$after = array('i', 'g', 'u', 's', 'o', 'c', 'i', 'g', 'u', 'o', 'c'); // , '', ''

	$clean = str_replace($before, $after, $str);
	$clean = preg_replace('/[^a-zA-Z0-9 ]/', '', $clean);
	$clean = preg_replace('!\s+!', '-', $clean);
	$clean = strtolower(trim($clean, '-'));

	return $clean;
}

function getSurveyOptions($question_id, $required, $type, $multiple, $order, $maxLength = null, $survey_page = 'survey')
{
	$CI = get_instance();
	$options = $CI->db->select('*')
		->from('survey_options')
		->where([
			'question_id' => $question_id
		])
		->order_by('survey_options.order', 'asc')
		->get()
		->result();
	$html = "";
	if ($required) {
		$req = "required";
	} else {
		$req = "";
	}
	if (!empty($maxLength)) {
		$maxLength = 'maxlength = "' . $maxLength . '"';
	}
	if ($multiple) {
		$mul = "multiple";
		$name = "q" . $order . "[]";
		if ($required) {
			$mul_class = 'multiple-options';
		} else {
			$mul_class = '';
		}
	} else {
		$mul = "";
		$name = "q" . $order;
		$mul_class = 'multiple-options';
	}
	if ($survey_page != 'modal') {
		$select2 = 'select2';
	} else {
		$select2 = '';
	}
	if ($type == "Selectbox") {
		$html .= '<select name="' . $name . '" class="form-control col-lg-4 col-md-6 col-sm-12 col-xs-12 ' . $select2 . '" id="question' . $question_id . '" ' . $req . ' ' . $mul . ' >';
		if (!$mul) {
			$html .= '<option disabled selected value>{please_select_answer}</option>';
		}
		foreach ($options as $option) {
			$html .= '<option data-order="' . $option->order . '" data-id="' . $option->id . '" value="' . $option->option . '">' . $option->option . '</option>';
		}
		$html .= '</select>';
	} elseif ($type == "Radio") {
		$html .= '<div class="survey-radios-group">';
		foreach ($options as $key => $option) {

			$html .= '<div class="survey-radio-group">
                            <input type="radio" class="survey-radio-input" id="radio' . $order . '-' . $key . '" name="' . $name . '" value="' . ($option->option) . '"  ' . $req . ' />
                            <label class="survey-radio-label" for="radio' . $order . '-' . $key . '"></label>
                            <label for="radio' . $order . '-' . $key . '" class="survey-answer">' . $option->option . '</label>
                        </div>';
		}
		$html .= '</div>';
	} elseif ($type == "Checkbox") {
		$html .= '<div class="survey-checkbox-group ' . $mul_class . '">';
		foreach ($options as $key => $option) {
			$html .= '<label class="survey__label" for="cb' . $order . '-' . $key . '">
                                <input id="cb' . $order . '-' . $key . '" name="' . $name . '" type="checkbox" class="click-checkbox-3" value="' . ($option->option) . '"   ' . $req . ' />
                                <span class="checkbox-custom"></span>
                                <span class="survey__text">' . $option->option . '</span>
                            </label>';
		}
		$html .= '</div>';
	} elseif ($type == "Freetext") {
		$html .= '<div class="survey-free-text">';
		$html .= '<textarea id="text-area-' . $order . '" class="form-control" name="' . $name . '" rows="3" cols="50" ' . $maxLength . ' ' . $req . '></textarea>';
		$html .= '</div>';
	} elseif ($type == "Yes-No") {
		$html .= '<div class="d-flex">
                        <label class="survey__label register-label flex-center-row  mr-sm" for="cb' . $order . '-1">
                            <input id="cb' . $order . '-1" name="' . $name . '" type="radio" class="click-checkbox-3" value="Yes" ' . $req . '/>
                            <span class="checkbox-custom rectangular mt-8"></span>
                            <img src="/assets/common/general/images/icon-hand-green.png" alt="icon" class="survey__icon">
                        </label>

                        <label class="survey__label register-label flex-center-row mt-8" for="cb' . $order . '-2">
                            <input id="cb' . $order . '-2" name="' . $name . '" type="radio" class="click-checkbox-3" value="No" ' . $req . ' />
                            <span class="checkbox-custom rectangular mt-"></span>
                            <img src="/assets/common/general/images/icon-hand-red.png" alt="icon" class="survey__icon">
                        </label>
                    </div>';
	} elseif ($type == "Star") {
		$html .= '<div class="cont">
            <div class="stars radio ' . $req . '" style="max-width:calc(' . count($options) . ' * 75px)">';
		foreach (array_reverse($options) as $key => $option) {
			$html .= '<input class="star star-' . (count($options) - $key) . '" id="star-' . $order . '-' . (count($options) - $key) . '" type="radio" name="' . $name . '" value="' . $option->option . '" ' . $req . ' />
                            <label class="star star-' . (count($options) - $key) . '" for="star-' . $order . '-' . (count($options) - $key) . '"></label>';
		}
		$html .= '</div>
            </div>';
	}
	return $html;
}

function getSurveyForm($survey_no = 1, $is_post = true, $survey_page = "survey")
{
	$CI = get_instance();
	$project_id = $CI->session->userdata('site_config')->project_id;
	$questions = $CI->db
		->select('*')
		->from('survey_questions')
		->where([
			'project_id' => $project_id,
			'survey_no' => $survey_no,
			'is_post' => $is_post
		])
		->order_by('survey_questions.order', 'asc')
		->get()
		->result();
	$html = '';
	$html .= '<form role="form" id="survey" method="post">';
	$html .= getSurveySuccessMessage('dn') . getSurveyFailMessage();

	if (!$is_post) {
		$html .= '<input type="hidden" name="survey_type" value="pre">';
	}

	if (count($questions) > 0) {
		$html .= '<input type="hidden" name="survey_no" value="' . $survey_no . '">';
		foreach ($questions as $key => $question) {
			$html .= '<div class="survey__group">
								<label>' . ($key + 1) . '. ' . $question->question . '</label>' . getSurveyOptions($question->id, $question->required, $question->type, $question->multiple, $key + 1, $question->max_length, $survey_page) . '
							</div>';
		}
		$html .= '<input type="hidden" name="survey_page" value="' . $survey_page . '">';
		$html .= '<div class="survey__button-event">
                        <button class="btn-next btn-animate mb-3">
                            {save}
                        </button>
                    </div>';
		$html .= '</form>';
	}
	$survey_name = 'survey_done' . $survey_no;
	if ($CI->session->userdata($survey_name)) {
		echo getSurveySuccessMessage();
	} else {
		echo $html;
	}
}

function getSurveyCustomMessage($dn = "")
{

	$html = '<div class="flex-center ' . $dn . '">
                <img src="/assets/common/general/imgDefaultPages/icon-check-green.png" alt="Icon" class="card-thakyou__icon">
                <h2 class="card-thakyou__title color-navy-blue-2">{thankyou_taking_survey}</h2>';

	if (project_config('upcoming_event')) {
		$html .= '<p class="card-thakyou__desciription color-navy-blue-2">
                        {see_you_next_event}
                    </p>
                    <div class="card-upcoming bg-blue-light mb-5">
                        <div class="card-upcoming__head bg-navy-blue">
                            <p class="card-upcoming__date color-white">
                                {next_event_date}
                            </p>
                        </div>
                        <div class="card-upcoming__body">
                            <p class="card-upcoming__info color-navy-blue-2">
                                {next_event_text}
                            </p>
                            <span class="card-upcoming__border"></span>

                            <p class="card-upcoming__speaker-name color-blue">
                                {next_event_speaker}
                            </p>
                        </div>
                        <div class="card-upcoming__footer">
                            <a href="#" class="btn-upcoming-register color-white bg-blue m-2">{register}</a>
                            <a href="#" class="btn-upcoming-calendar color-white bg-navy-blue-2 m-2">
                                {add_to_calendar}
                            </a>
                        </div>
                    </div>';
	}
	$html .= '</div>';
	return $html;
}

function getSurveySuccessMessage($dn = "")
{
	return project_config("surveySuccessMessage") ? '<div id="surveySuccessMessage" class="' . $dn . '">' . project_config("surveySuccessMessage") . '</div>' : '<div id="surveySuccessMessage" class="' . $dn . '"><h3>{survey_thank_you}</h3></div>';
}

function getSurveyFailMessage()
{
	return project_config("surveyHiddenErrorMessage") ? project_config("surveyHiddenErrorMessage") : '<div id="surveyFailMessage" class="dn"><h3 class="text-danger">{survey_error}</h3></div>';
}


function public_chat_form()
{
	$CI = get_instance();
	if (!$CI->session->userdata('site_user')) {
		return null;
	}
	$project_id = $CI->session->userdata('site_config')->project_id;
	@$fullname = $CI->session->userdata('site_user')->fullname;

	return '<link rel="stylesheet" href="/assets/common/general/css/public_chat.css"/>
		<button class="chat_button">
			<i class="fa fa-comment fa-2x" aria-hidden="true"></i>
		</button>
		<section class="public_chat chatbox-popup">
		  <header class="chatbox-popup__header">
			<aside style="flex:10">
			  <h1>' . @$fullname . '</h1>
			</aside>
			<aside style="flex:2">
				<button class="chatbox-panel-close header_button"><i class="fa fa-close" aria-hidden="true"></i></button>
				<button class="chatbox-maximize header_button"><i class="fa fa-expand" aria-hidden="true"></i></button>
			</aside>
		  </header>
		  <main id="chatbox_messages_content" class="chatbox-popup__main chatbox_messages_content">
			{no_messsage_text}
		  </main>
		  <footer class="chatbox-popup__footer">
			<aside style="flex:10">
			  <textarea class="chatbox_textbox" type="text" placeholder="{type_your_message}" autofocus></textarea>
			</aside>
			<aside class="chatbox_send_button" style="flex:1;color:#888;text-align:center;">
			  <i class="fa fa-paper-plane" aria-hidden="true"></i>
			</aside>
		  </footer>
		</section>
		<section class="public_chat chatbox-panel">
		  <header class="chatbox-panel__header">
			<aside style="flex:9">
			  <h1>' . @$fullname . '</h1>
			</aside>
			<aside style="flex:3;text-align:right;">
			  <button class="chatbox-minimize"><i class="fa fa-minus" aria-hidden="true"></i></button>
			  <button class="chatbox-panel-close"><i class="fa fa-close" aria-hidden="true"></i></button>
			</aside>
		  </header>
		  <main id="chatbox_messages_content2" class="chatbox-panel__main chatbox_messages_content" style="flex:1">
		  {no_messsage_text}
		  </main>
		  <footer class="chatbox-panel__footer">
			<aside style="flex:10">
			  <textarea class="chatbox_textbox" type="text" placeholder="{type_your_message}" autofocus></textarea>
			</aside>
			<aside class="chatbox_send_button" style="flex:1;color:#888;text-align:center;">
			  <i class="fa fa-paper-plane" aria-hidden="true"></i>
			</aside>
		  </footer>
		</section>
		<script src="/assets/common/general/js/public_chat.js?v=' . date('Ymd') . '"></script>
		<script>
			setInterval(function(){ get_messages(); }, 5000);
		</script>';
}

function getArray($config)
{
	if (project_config($config)) {
		$array = project_config($config);
		$array = explode(',', $array);
		$array = array_map('trim', $array);
		$array = array_map('strtolower', $array);
		return $array;
	} else {
		return null;
	}
}

function getVideos($pid = 0)
{
	$CI = get_instance();
	if ($pid == 0) {
		$pid = $CI->session->userdata('site_config')->project_id;
	}
	$cur_date = date('Y-m-d');
	$country = user_info('country', '');

	$videos = $CI->db->select('*')->from('ondemandvideo')
		->where(['project_id' => $pid])
		->where('stream_date <=', $cur_date)
		->where('expired_date >=', $cur_date)
		->group_start()
		->where('all_countries', 1)->or_like('country', $country)
		->group_end()
		->get()->result();
	return $videos;
}

function getVideoStatus($video_id, $user_id = 0)
{
	$CI = get_instance();
	if ($user_id == 0) {
		$user_id = user_info('reg_id') ? user_info('reg_id') : user_info('log_id');
	}
	$status = $CI->db->select('*')->from('video_status')
		->where(['user_id' => $user_id, 'video_id' => $video_id])
		->get()->row();
	return $status ? $status->status : 'locked';
}

function setVideoStatus($status, $video_id, $user_id = 0)
{
	$CI = get_instance();
	$pid = $CI->session->userdata('site_config')->project_id;
	if ($user_id == 0) {
		$user_id = user_info('reg_id') ? user_info('reg_id') : user_info('log_id');
	}
	$data['status'] = $status;
	$status = $CI->db->where(['user_id' => $user_id, 'video_id' => $video_id])->update('video_status', $data);
	return $set ? true : false;
}

function file_get_contents_ex($url, $cookie = null, $proxy = null, $auth = null)
{
	$opts = array(
		'http' => array(
			'method' => 'GET',
			'user-agent' => 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/48.0.2564.109 Safari/537.36',
			'header' => "Accept-language: en\r\n" .
				(!empty($auth) ? "Proxy-Authorization: Basic {$auth}\r\n" : '') .
				(!empty($cookie) ? "Cookie: " . implode('; ', (array) $cookie) . "\r\n" : ''),
		),
		"ssl" => array(
			"verify_peer" => false,
			"verify_peer_name" => false,
		)
	);
	if (!empty($proxy)) {
		$opts['http']['proxy'] = $proxy;
		$opts['http']['request_fulluri'] = true;
	}

	$context = stream_context_create($opts);

	// Open the file using the HTTP headers set above
	return file_get_contents($url, false, $context);
}

if (!function_exists('get_custom_lang')) {
	function get_custom_lang($key)
	{
		$key = str_replace(["{", "}"], "", $key);
		$CI = get_instance();
		$pid = $CI->session->userdata('site_config')->project_id;
		$current_lang =
			$CI->session->userdata('site_config')->current_lang ?:
			$CI->session->userdata('site_config')->default_lang;
		$CI->db->select('value as value'); //'as' value olmazsa b�y�k harfli geliyor VALUE �eklinde ??
		$CI->db->where([
			'project_id' => $pid,
			'lang_id' => $current_lang,
			'key' => $key
		]);
		$custom = $CI->db->from('lang_custom')->get()->row();
		if (!$custom) {
			return $key;
		} else {
			$result = $custom;
			return $result->value;
		}
	}
}

if (!function_exists('get_video_html')) {
	function get_video_html($video)
	{
		if (strpos($video->web, 'youtube')) {
			preg_match('/src="([^"]+)"/', $video->web, $match);
			$url = $match[1];
			$code = explode("/", $url);
			$img_url = "https://img.youtube.com/vi/" . end($code) . "/hqdefault.jpg";
		} else {
			$img_url = '/file/ondemand_video/' . $video->img_url;
		}


		return '<div class="video-head__info video_image video' . $video->id . '" data-id="' . $video->id . '" data-category_id="' . $video->category_id . '" onclick="get_video(' . $video->id . ')">
                            <img src="' . $img_url . '" class="video-head__img">
                            <p class="video-head__topic"><span class="video_title">' . get_custom_lang($video->title) . '</span>
                                <span class="video-head__subtitle video_subtitle">' . get_custom_lang($video->description) . '</span>
                                <span class="video-head__speaker video_speaker">' . get_custom_lang($video->name) . '</span>
                            </p>
                        </div>';
	}
}


if (!function_exists('get_pdf_html')) {
	function get_pdf_html($pdf)
	{
		if (strpos($pdf->web, 'youtube')) {
			preg_match('/src="([^"]+)"/', $pdf->web, $match);
			$url = $match[1];
			$code = explode("/", $url);
			$img_url = "https://img.youtube.com/vi/" . end($code) . "/hqdefault.jpg";
		} else {
			$img_url = '/file/ondemand_video/' . $pdf->img_url;
		}

		return '<div class="document-head__info myli pdf' . $pdf->id . '" data-id="' . $pdf->id . '" data-category_id="' . $pdf->category_id . '" onclick="get_pdf(' . $pdf->id . ')">
            <img src="' . $img_url . '"" class="document-head__img">
            <div class="document-head__box">
                <div class="document-head__pstn">
                    <img src="/file/image/document-icon-gray.png" class="document-head__download">
                    <p class="document-head__topic">' . get_custom_lang($pdf->title) . '
                </div>
                <a href="/pdf?pdf=' . $pdf->web . '" target="_blank" class="document-head__link detail_image detail_title">Mostrar documento</a>
            </div>
            </p>
        </div>';
	}
}

if (!function_exists('get_podcast_html')) {
	function get_podcast_html($video)
	{
		return '<div class="podcast-head__info podcast' . $video->id . '" data-id="' . $video->id . '" data-category_id="' . $video->category_id . '" onclick="get_video(' . $video->id . ')">
						<img src="/file/ondemand_video/' . $video->img_url . '" class="podcast-head__img video_image">
						<div class="podcast-head__flex">
							<div class="podcast-head__pstn">
								<img src="/file/image/podcast-sound.png" class="podcast-head__sound">
								<p class="podcast-head__title"><span class="video_title">' . get_custom_lang($video->title) . '</span>
									<span class="podcast-head__text video_subtitle">' . get_custom_lang($video->description) . '</span>
									<span class="podcast-head__area video_speaker">' . get_custom_lang($video->name) . '</span>
								</p>
							</div>
							<img src="/file/image/podcast-wave.png" class="podcast-head__wave">
						</div>
					</div>';
	}
}


/**
 * Logger functions
 */

function tracked_time_logger($page)
{
	$status = false;
	try {
		$CI = get_instance();
		$logged_user = $CI->session->userdata('site_user');
		if (!empty($logged_user) && !empty($logged_user->email)) {
			$project_id = $CI->session->userdata('site_config')->project_id;
            $embed_lang = $CI->session->userdata('site_config')->stream_lang ?? null;
			$existData = $CI->m_router->get_tracked_time_row($project_id, $logged_user->email, $page,$embed_lang);
			if (empty($existData)) {
				$CI->m_router->set_tracked_time($project_id, $logged_user->email, $page, $logged_user->session ?? null, $embed_lang);
			} elseif (strtotime('now') - $existData->logout_timestamp > 60) {
				$CI->m_router->set_tracked_time($project_id, $logged_user->email, $page, $logged_user->session ?? null, $embed_lang);
			} else {
				$CI->m_router->update_tracked_time($existData->id, $project_id, $logged_user->email, $page, $embed_lang);
			}
			$status = true;
		}
	} catch (\Exception $e) {
		$status = false;
	}
	return $status;
}


function recording_time_logger()
{
	$CI = get_instance();
	$is_active = $CI->input->post('value');
	$title = $CI->input->post('title');

	try {
		if (($webinar_is_completed = project_config('webinar_is_completed')) !== false) {
			if ($is_active == "0" && $webinar_is_completed === "0") {
				update_config('webinar_is_completed', 1);
			} elseif ($is_active == "1" && $webinar_is_completed === "1") {
				update_config('webinar_is_completed', 0);
			}
		}

		$project_id = $CI->session->userdata('site_config')->project_id;

		$existData = $CI->m_router->get_recording_time_row($project_id);

		if (empty($existData)) {
			$CI->m_router->set_recording_time($project_id);
			$is_active = 1;
		} else {
			$CI->m_router->update_recording_time($existData->id, $project_id, $title);
			$is_active = 0;
		}
		$status = true;
	} catch (\Exception $e) {
		$status = false;
	}
	return [
		'status' => $status,
		'is_active' => $is_active
	];
}

function recording_time_update_title($id, $title)
{
	$CI = get_instance();

	try {
		$project_id = $CI->session->userdata('site_config')->project_id;
		$CI->m_router->update_recording_time_title($id, $project_id, $title);

		$status = true;
	} catch (\Exception $e) {
		$status = false;
	}
	return [
		'status' => $status,
	];
}


function generate_timezone_list()
{
	static $regions = array(
		DateTimeZone::AFRICA,
		DateTimeZone::AMERICA,
		DateTimeZone::ANTARCTICA,
		DateTimeZone::ASIA,
		DateTimeZone::ATLANTIC,
		DateTimeZone::AUSTRALIA,
		DateTimeZone::EUROPE,
		DateTimeZone::INDIAN,
		DateTimeZone::PACIFIC,
	);

	$timezones = array();
	foreach ($regions as $region) {
		$timezones = array_merge($timezones, DateTimeZone::listIdentifiers($region));
	}

	$timezone_offsets = array();
	foreach ($timezones as $timezone) {
		$tz = new DateTimeZone($timezone);
		$timezone_offsets[$timezone] = $tz->getOffset(new DateTime);
	}

	// sort timezone by offset
	asort($timezone_offsets);

	$timezone_list = array();
	foreach ($timezone_offsets as $timezone => $offset) {
		$offset_prefix = $offset < 0 ? '-' : '+';
		$offset_formatted = gmdate('H:i', abs($offset));

		$pretty_offset = "UTC${offset_prefix}${offset_formatted}";

		$timezone_list[$timezone] = "(${pretty_offset}) $timezone";
	}

	return $timezone_list;
}

function strLimit($text, $limit = 50, $append = null)
{
	if (strlen($text) <= $limit) {
		return $text;
	}
	return substr($text, 0, $limit) . (!empty($append) ? $append : '');
}


function isStringDate($date)
{
	if (is_numeric($date) || preg_match("/\d{4}-\d{2}-\d{2}/", $date) === 0) {
		return false;
	}

	$timestamp = strtotime($date);
	return (bool) $timestamp;
}

function humanDate($date, $showTimezone = false)
{
	if (is_numeric($date)) {
		return date($showTimezone ? 'd/m/Y H:i P' : 'd/m/Y H:i', $date);
	}

	return date($showTimezone ? 'd/m/Y H:i P' : 'd/m/Y H:i', strtotime($date));
}

function createAuthLink($provider): string
{
	$provider = strtolower(trim($provider));
	$providers = [
		'google' => 'GoogleService',
		'linkedin' => 'LinkedinService',
		'apple' => 'AppleService',
	];
	if (isset($providers[$provider])) {
		require_once APPPATH . "libraries/Auth/{$providers[$provider]}.php";
		$provider = new $providers[$provider]();
		return $provider->createAuthLink($provider);
	}
	return "";
}

function getSecretCredential($key): string
{
	$CI = get_instance();
	$data = $CI->db->from('sc_storage')
		->where('project_id', site_config('project_id'))
		->where('key', $key)
		->get()
		->row();
	return !empty($data->value) ? decryptText($data->value) : "";
}

function getStreamCodes()
{
	$CI = get_instance();
	$data = $CI->db->from('project')
		->where('id', site_config('project_id'))
		->get()
		->row();
	return !empty($data->stream_code) ? json_decode($data->stream_code ?? "[]") : [];
}

function getSessionList()
{
	$CI = get_instance();
	mysqli_next_result($CI->db->conn_id);
	return $CI->db->from('records')
		->where('project_id', site_config('project_id'))
		->get()
		->result();
}

function getEmbedList()
{
	$CI = get_instance();
	$project_id = $CI->session->userdata('site_config')->project_id;
	$where = [
		'project_id' => $project_id
	];
	$CI->db->select('lang,name');
	$CI->db->where($where);
	$CI->db->from('embed');
	$CI->db->order_by('order', 'asc');
	$result = $CI->db->get()->result();
	return count($result) > 0 ? $result : null;
}
