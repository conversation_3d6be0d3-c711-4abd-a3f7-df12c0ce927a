<?php

class Command extends CI_Controller
{
    const AVAILABLE_COMMANDS = [
        'registration-mail',
        'transaction-mail-panel',
        'reminder-mail',
        'sendgrid:contact-clear',
        'rabbitmq:consume',
        'rabbitmq:publish'
    ];

    public function __construct()
    {
        parent::__construct();
        if (!is_cli()) exit('Only CLI access allowed');

        // Konsol komutu çalıştırıldığında domain tespit edilemediği için komut içerisinde bu bilgi gönderilerek kullanılabilir
    }

    public function index($type)
    {
        if (in_array($type, self::AVAILABLE_COMMANDS)) {
            print_r($type . ' command started!' . PHP_EOL);
            $this->executeCommand($type);
            print_r($type . ' command completed!' . PHP_EOL);
        } else {
            print_r("Command not found" . PHP_EOL);
        }
    }

    public function withValue($type, $param = null)
    {
        if (in_array($type, self::AVAILABLE_COMMANDS)) {
            print_r($type . ' command started! ' . date('Y-m-d H:i:s') . PHP_EOL);
            $this->executeCommand($type, $param);
            print_r($type . ' command completed! ' . date('Y-m-d H:i:s') . PHP_EOL);
        }
    }

    /**
     * Execute the appropriate command based on type
     *
     * @param string $type Command type
     * @param mixed $param Optional parameter for the command
     * @return void
     */
    private function executeCommand($type, $param = null)
    {
        // Load the appropriate command class
        $commandClass = $this->getCommandClass($type);

        if ($commandClass) {
            require_once APPPATH . 'controllers/commands/' . $commandClass . '.php';
            $command = new $commandClass();
            $command->execute($param);
        } else {
            print_r("Command not found" . PHP_EOL);
        }
    }

    /**
     * Get the command class name based on command type
     *
     * @param string $type Command type
     * @return string|null Command class name or null if not found
     */
    private function getCommandClass($type)
    {
        $commandMap = [
            'registration-mail' => 'RegistrationMailCommand',
            'transaction-mail-panel' => 'TransactionMailPanelCommand',
            'reminder-mail' => 'ReminderMailCommand',
            
            // 'rabbitmq:consume' => 'RabbitMQConsumerCommand',
            // 'rabbitmq:publish' => 'RabbitMQPublisherCommand'

            'sendgrid:contact-clear' => 'SendgridContactClearCommand',
        ];

        return isset($commandMap[$type]) ? $commandMap[$type] : null;
    }
}
