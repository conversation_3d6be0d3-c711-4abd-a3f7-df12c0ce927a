<?php

require_once APPPATH . 'controllers/commands/BaseCommand.php';

class RabbitMQPublisherCommand extends BaseCommand
{
    public function execute($param = null)
    {
        $this->CI->load->library('RabbitMQ');
        
        // Parse the parameter to get queue and message
        $queue = 'vistream_default';
        $message = '';
        
        if (!empty($param)) {
            $parts = explode(':', $param, 2);
            if (count($parts) >= 1) {
                $queue = $parts[0];
            }
            if (count($parts) >= 2) {
                $message = $parts[1];
            }
        }
        
        if (empty($message)) {
            $message = [
                'timestamp' => time(),
                'message' => 'Test message from RabbitMQ publisher',
                'date' => date('Y-m-d H:i:s')
            ];
        } else {
            // Try to parse as JSON
            $json = json_decode($message, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $message = $json;
            }
        }
        
        echo "Publishing message to queue: $queue\n";
        
        // Publish the message
        $result = $this->CI->rabbitmq->publish($queue, $message);
        
        if ($result) {
            echo "Message published successfully\n";
        } else {
            echo "Failed to publish message\n";
        }
    }
}
