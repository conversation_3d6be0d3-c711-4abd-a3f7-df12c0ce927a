<?php

require_once APPPATH . 'controllers/commands/BaseCommand.php';

class SendgridContactClearCommand extends BaseCommand
{
    private SendGridService $service;
    private array $arguments = [
        'request',
        'transfer',
        'delete'
    ];

    public function __construct()
    {
        parent::__construct();

        $this->CI->load->helper('sendgrid_helper');
        $this->service = new SendGridService(true);
    }

    public function execute($argument = null)
    {
        if (!in_array($argument, $this->arguments)) {
            return null;
        }
        return $this->$argument();
    }

    private function request()
    {
        print_r("Exports preparing! Clear job will run after exports are ready." . PHP_EOL);

        /* Collect List IDs */
        $lists = $this->service->getLists(true);

        /* Initiate CSV Export Requests */
        $exports = [];
        foreach ($lists as $list) {
            print_r("Exporting list: " . $list->sendgrid_id . PHP_EOL);
            $exports[$list->sendgrid_id] = $this->service->exportContacts($list->sendgrid_id);
            sleep(5);
        }
    }

    private function transfer()
    {
        print_r("Checking export status!" . PHP_EOL);

        /* Collect CSV Results */
        $count = $this->service->getExportingCount();
        print_r("Pending Exports: " . ($count ?? 0) . PHP_EOL);

        /* Retrieve Exported CSV Files */
        $this->service->retrieveExports();
        print_r("Exports completed!" . PHP_EOL);

        /* Save CSV Files To Database */
        $this->service->saveExportLists();
    }

    private function delete()
    {
        print_r('Cleaning...' . PHP_EOL);
        $this->service->clearUnsedContacts();
    }
}
