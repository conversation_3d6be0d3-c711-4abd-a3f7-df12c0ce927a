<?php

require_once APPPATH . 'controllers/commands/BaseCommand.php';

class RabbitMQConsumerCommand extends BaseCommand
{
    public function execute($param = null)
    {
        $this->CI->load->library('RabbitMQ');
        
        // Determine which queue to consume from
        $queue = 'vistream_default';
        if (!empty($param)) {
            $queue = $param;
        }
        
        echo "Starting consumer for queue: $queue\n";
        
        // Define the callback function
        $callback = function ($msg) {
            echo "Received message: " . $msg->body . "\n";
            
            // Process the message
            $data = json_decode($msg->body, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                echo "Processing message data...\n";
                print_r($data);
                // Add your processing logic here
            } else {
                echo "Invalid JSON in message\n";
            }
            
            // Acknowledge the message
            $msg->ack();
            
            echo "Message processed\n";
        };
        
        // Start consuming messages
        $this->CI->rabbitmq->consume($queue, $callback, false);
    }
}
